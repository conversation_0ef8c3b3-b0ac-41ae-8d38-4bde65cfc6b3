const express = require('express');
const router = express.Router();
const ProductController = require('../controllers/productController');
const { cacheMiddleware } = require('../config/redis');
const { uploadProductImages } = require('../middleware/uploadMiddleware');

router.route('/').post(uploadProductImages, ProductController.createProduct);
router.route('/').get(cacheMiddleware(300), ProductController.getAllProducts);
router.route('/:id').get(cacheMiddleware(300), ProductController.getProduct);
router.route('/:id').patch(uploadProductImages, ProductController.updateProduct);
router.route('/:id').delete(ProductController.deleteProduct);

module.exports=router