/**
 * GridFS configuration for MongoDB
 * This provides functionality to store and retrieve files from MongoDB using GridFS
 */
const mongoose = require('mongoose');
const { GridFSBucket } = require('mongodb');
const { config } = require('dotenv');
config();

// Initialize GridFS bucket
let gfs;
let gridFSBucket;

// Connect to GridFS
const connectGridFS = async () => {
  try {
    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      throw new Error('MongoDB not connected');
    }

    // Initialize GridFS bucket
    gridFSBucket = new GridFSBucket(mongoose.connection.db, {
      bucketName: 'uploads'
    });

    console.log('GridFS connected successfully');
    return true;
  } catch (error) {
    console.error('GridFS connection error:', error);
    return false;
  }
};

// Get the GridFS bucket
const getGridFSBucket = () => {
  if (!gridFSBucket) {
    throw new Error('GridFS bucket not initialized');
  }
  return gridFSBucket;
};

// Find a file by filename
const findFileByFilename = async (filename) => {
  try {
    const bucket = getGridFSBucket();
    const files = await bucket.find({ filename }).toArray();
    return files.length > 0 ? files[0] : null;
  } catch (error) {
    console.error('Error finding file by filename:', error);
    return null;
  }
};

// Find a file by ID
const findFileById = async (id) => {
  try {
    const bucket = getGridFSBucket();
    const files = await bucket.find({ _id: new mongoose.Types.ObjectId(id) }).toArray();
    return files.length > 0 ? files[0] : null;
  } catch (error) {
    console.error('Error finding file by ID:', error);
    return null;
  }
};

// Delete a file by filename
const deleteFileByFilename = async (filename) => {
  try {
    const file = await findFileByFilename(filename);
    if (!file) {
      return false;
    }
    
    const bucket = getGridFSBucket();
    await bucket.delete(file._id);
    return true;
  } catch (error) {
    console.error('Error deleting file by filename:', error);
    return false;
  }
};

// Delete a file by ID
const deleteFileById = async (id) => {
  try {
    const bucket = getGridFSBucket();
    await bucket.delete(new mongoose.Types.ObjectId(id));
    return true;
  } catch (error) {
    console.error('Error deleting file by ID:', error);
    return false;
  }
};

// Create a read stream for a file
const createReadStream = (fileId) => {
  try {
    const bucket = getGridFSBucket();
    return bucket.openDownloadStream(new mongoose.Types.ObjectId(fileId));
  } catch (error) {
    console.error('Error creating read stream:', error);
    throw error;
  }
};

module.exports = {
  connectGridFS,
  getGridFSBucket,
  findFileByFilename,
  findFileById,
  deleteFileByFilename,
  deleteFileById,
  createReadStream
};
