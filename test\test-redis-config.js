/**
 * Test script for the updated Redis configuration
 * Run with: node test-redis-config.js
 */

// Import the Redis configuration
const {
  redisClient,
  connectRedis,
  cacheMiddleware,
  clearCache,
} = require("./config/redis");

// Test the Redis connection
async function testRedisConfig() {
  try {
    console.log("Testing Redis configuration...");

    // Test connection
    const connected = await connectRedis();
    console.log("Connection test result:", connected);

    if (!connected) {
      console.error("Failed to connect to Redis. Check your Redis URL.");
      return;
    }

    // Test basic operations
    console.log("\nTesting basic Redis operations:");

    // Set a test key
    const setResult = await redisClient.set(
      "test:config",
      "Testing Redis configuration"
    );
    console.log("✅ SET operation successful:", setResult);

    // Get the test key
    const getValue = await redisClient.get("test:config");
    console.log(`✅ GET operation successful: ${getValue}`);

    // Test cache clearing
    await redisClient.set("test:pattern:1", "Value 1");
    await redisClient.set("test:pattern:2", "Value 2");
    await redisClient.set("test:pattern:3", "Value 3");

    console.log("\nTesting cache clearing:");
    await clearCache("test:pattern:*");

    // Verify keys were deleted
    const key1 = await redisClient.get("test:pattern:1");
    console.log("Key 1 after clearing:", key1);

    // Clean up
    await redisClient.del("test:config");

    console.log("\n🎉 Redis configuration test completed successfully!");
  } catch (error) {
    console.error("Error testing Redis configuration:", error);
  }
}

// Run the test
testRedisConfig();
