const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");
const mongoose = require("mongoose");

// Load environment variables first
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Validate required environment variables
const requiredEnvVars = ['MONGO_URI', 'JWT_SECRET'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars);
  console.error('Available environment variables:', Object.keys(process.env).filter(key => key.includes('MONGO') || key.includes('JWT')));
}

console.log('Environment check:', {
  NODE_ENV: process.env.NODE_ENV,
  MONGO_URI_EXISTS: !!process.env.MONGO_URI,
  JWT_SECRET_EXISTS: !!process.env.JWT_SECRET,
  REDIS_URL_EXISTS: !!process.env.REDIS_URL,
});

// Global connection state for serverless
let isConnected = false;
let connectionPromise = null;

// Database connection function optimized for serverless
const connectToDatabase = async () => {
  if (isConnected && mongoose.connection.readyState === 1) {
    console.log('Using existing database connection');
    return mongoose.connection;
  }

  if (connectionPromise) {
    console.log('Waiting for existing connection promise');
    return connectionPromise;
  }

  if (!process.env.MONGO_URI) {
    throw new Error('MONGO_URI environment variable is not set');
  }

  console.log('Creating new database connection');

  connectionPromise = mongoose.connect(process.env.MONGO_URI, {
    maxPoolSize: 5, // Reduce pool size for serverless
    serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
    socketTimeoutMS: 30000, // Reduce socket timeout for serverless
    connectTimeoutMS: 10000, // Connection timeout
    bufferCommands: false, // Disable mongoose buffering
  });

  try {
    await connectionPromise;
    isConnected = true;
    console.log('MongoDB connected successfully');

    // Handle connection events (only set once)
    if (!mongoose.connection.listeners('disconnected').length) {
      mongoose.connection.on('disconnected', () => {
        console.log('MongoDB disconnected');
        isConnected = false;
        connectionPromise = null;
      });

      mongoose.connection.on('error', (err) => {
        console.error('MongoDB connection error:', err);
        isConnected = false;
        connectionPromise = null;
      });
    }

    return mongoose.connection;
  } catch (error) {
    console.error('MongoDB connection failed:', error);
    isConnected = false;
    connectionPromise = null;
    throw error;
  }
};

// Import modules with error handling
let swaggerUi, swaggerSpecs, connectRedis, connectGridFS;
let authRoutes, userRoutes, productRouter, orderRouter, contactRoutes, imageRoutes, videoBlogRoutes;

try {
  console.log('Loading config modules...');
  swaggerUi = require("swagger-ui-express");
  swaggerSpecs = require("../config/swagger");
  ({ connectRedis } = require("../config/redis"));
  ({ connectGridFS } = require("../config/gridfs"));
  console.log('Config modules loaded successfully');

  console.log('Loading route modules...');
  try {
    authRoutes = require("../routes/authRoutes");
    console.log('✅ Auth routes loaded');
  } catch (err) {
    console.error('❌ Auth routes failed:', err.message);
  }

  try {
    userRoutes = require("../routes/userRoutes");
    console.log('✅ User routes loaded');
  } catch (err) {
    console.error('❌ User routes failed:', err.message);
  }

  try {
    productRouter = require("../routes/gridfsProductRoutes");
    console.log('✅ Product routes loaded');
  } catch (err) {
    console.error('❌ Product routes failed:', err.message);
  }

  try {
    orderRouter = require("../routes/orderRoutes");
    console.log('✅ Order routes loaded');
  } catch (err) {
    console.error('❌ Order routes failed:', err.message);
  }

  try {
    contactRoutes = require("../routes/contactRoutes");
    console.log('✅ Contact routes loaded');
  } catch (err) {
    console.error('❌ Contact routes failed:', err.message);
  }

  try {
    imageRoutes = require("../routes/imageRoutes");
    console.log('✅ Image routes loaded');
  } catch (err) {
    console.error('❌ Image routes failed:', err.message);
  }

  try {
    videoBlogRoutes = require("../routes/videoBlogRoutes");
    console.log('✅ Video blog routes loaded');
  } catch (err) {
    console.error('❌ Video blog routes failed:', err.message);
  }

} catch (importError) {
  console.error('Error importing modules:', importError);
  console.error('Import error stack:', importError.stack);
}

const app = express();

// Global connection state
let redisConnected = false;
let gridfsConnected = false;

// Initialize connections with error handling
const initializeConnections = async () => {
  try {
    // Connect to MongoDB first (required)
    await connectToDatabase();

    // Try to connect to Redis (non-blocking, optional)
    if (connectRedis) {
      try {
        const redisTimeout = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Redis timeout')), 3000)
        );
        await Promise.race([connectRedis(), redisTimeout]);
        redisConnected = true;
        console.log('Redis connected successfully');
      } catch (redisError) {
        console.error('Redis connection failed:', redisError.message);
        console.log('Continuing without Redis caching...');
        redisConnected = false;
      }
    }

    // Try to connect to GridFS (non-blocking, optional)
    if (connectGridFS && isConnected) {
      try {
        const gridfsTimeout = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('GridFS timeout')), 3000)
        );
        await Promise.race([connectGridFS(), gridfsTimeout]);
        gridfsConnected = true;
        console.log('GridFS connected successfully');
      } catch (gridfsError) {
        console.error('GridFS connection failed:', gridfsError.message);
        console.log('Continuing without GridFS...');
        gridfsConnected = false;
      }
    }
  } catch (error) {
    console.error('Failed to initialize connections:', error);
    throw error;
  }
};

// CORS configuration
app.use(
  cors({
    origin: "*",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Swagger API Documentation (only if swagger modules loaded successfully)
if (swaggerUi && swaggerSpecs) {
  app.use(
    "/api-docs",
    swaggerUi.serve,
    swaggerUi.setup(swaggerSpecs, { explorer: true })
  );
}

// Root route - health check
app.get("/", (_, res) => {
  res.json({
    message: "Chinioti Wooden Art API is Healthy",
    documentation: `${process.env.VERCEL_URL || process.env.DEPLOYED_URL || 'http://localhost:5002'}/api-docs`,
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: "1.0.0",
    connections: {
      database: isConnected,
      redis: redisConnected,
      gridfs: gridfsConnected
    }
  });
});

// Auth callback fallback route
app.get("/auth-callback", (req, res) => {
  const { token, error, errorMessage } = req.query;

  if (error) {
    res.status(400).json({
      status: "error",
      message: errorMessage || "Authentication failed",
    });
  } else if (token) {
    res.json({
      status: "success",
      message: "Authentication successful",
      token,
      note: "This is the backend auth-callback endpoint. You should be redirected to the frontend.",
    });
  } else {
    res.status(400).json({
      status: "error",
      message: "Invalid auth callback request",
    });
  }
});

// API Routes (only if modules loaded successfully)
if (authRoutes) app.use("/api/auth", authRoutes);
if (userRoutes) app.use("/api/users", userRoutes);
if (productRouter) app.use("/api/products", productRouter);
if (orderRouter) app.use("/api/orders", orderRouter);
if (contactRoutes) app.use("/api/contact", contactRoutes);
if (imageRoutes) app.use("/api/images", imageRoutes);
if (videoBlogRoutes) app.use("/api/video-blogs", videoBlogRoutes);

// Handle 404 - use a more specific pattern
app.use((req, res) => {
  res.status(404).json({
    status: "error",
    message: "Route not found",
    path: req.originalUrl,
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error("Express error:", error);

  if (!res.headersSent) {
    res.status(500).json({
      status: "error",
      message: "Internal server error",
      error: process.env.NODE_ENV === "development" ? error.message : "Something went wrong",
    });
  }
});

// Serverless function handler
module.exports = async (req, res) => {
  // Set CORS headers for all requests
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    console.log('Serverless function invoked:', req.method, req.url);

    // For health check routes, don't initialize connections
    if (req.url === '/' || req.url === '/health') {
      const healthResponse = {
        message: "Chinioti Wooden Art API is Healthy",
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        version: "1.0.0",
        connections: {
          database: isConnected,
          redis: redisConnected,
          gridfs: gridfsConnected
        }
      };
      return res.status(200).json(healthResponse);
    }

    // For other routes, try to initialize connections with timeout
    try {
      const connectionTimeout = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Connection timeout')), 8000)
      );

      await Promise.race([initializeConnections(), connectionTimeout]);
    } catch (connectionError) {
      console.error('Connection initialization failed:', connectionError.message);

      // Continue without connections for non-critical routes
      if (req.url.includes('/api-docs') || req.url.includes('/auth-callback')) {
        // These routes can work without database
      } else {
        // For API routes that need database, return error
        return res.status(503).json({
          status: "error",
          message: "Service temporarily unavailable - database connection failed",
          error: process.env.NODE_ENV === "development" ? connectionError.message : "Database unavailable",
        });
      }
    }

    // Use the app to handle the request with timeout
    return new Promise((resolve) => {
      let resolved = false;

      const originalEnd = res.end;
      const originalJson = res.json;

      const resolveOnce = () => {
        if (!resolved) {
          resolved = true;
          resolve();
        }
      };

      res.end = function(...args) {
        resolveOnce();
        return originalEnd.apply(this, args);
      };

      res.json = function(...args) {
        resolveOnce();
        return originalJson.apply(this, args);
      };

      // Set a timeout for the entire request
      const requestTimeout = setTimeout(() => {
        if (!res.headersSent && !resolved) {
          res.status(500).json({
            status: "error",
            message: "Request timeout"
          });
        }
        resolveOnce();
      }, 25000); // 25 second timeout

      app(req, res).catch(err => {
        clearTimeout(requestTimeout);
        console.error("App error:", err);
        if (!res.headersSent && !resolved) {
          res.status(500).json({
            status: "error",
            message: "Internal server error",
            error: process.env.NODE_ENV === "development" ? err.message : "Something went wrong",
          });
        }
        resolveOnce();
      });
    });

  } catch (error) {
    console.error("Serverless function error:", error);
    console.error("Error stack:", error.stack);

    // Ensure we respond with proper JSON
    if (!res.headersSent) {
      return res.status(500).json({
        status: "error",
        message: "Internal server error",
        error: process.env.NODE_ENV === "development" ? error.message : "Something went wrong",
      });
    }
  }
};

// For local development
if (process.env.NODE_ENV !== "production") {
  const PORT = process.env.PORT || 5002;
  app.listen(PORT, async () => {
    await initializeConnections();
    console.log(`Server is running on http://localhost:${PORT}`);
  });
}