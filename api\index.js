const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");
const mongoose = require("mongoose");

// Load environment variables first
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Validate required environment variables
const requiredEnvVars = ['MONGO_URI', 'JWT_SECRET'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars);
  console.error('Available environment variables:', Object.keys(process.env).filter(key => key.includes('MONGO') || key.includes('JWT')));
}

console.log('Environment check:', {
  NODE_ENV: process.env.NODE_ENV,
  MONGO_URI_EXISTS: !!process.env.MONGO_URI,
  JWT_SECRET_EXISTS: !!process.env.JWT_SECRET,
  REDIS_URL_EXISTS: !!process.env.REDIS_URL,
});

// Global connection state for serverless
let isConnected = false;
let connectionPromise = null;

// Database connection function optimized for serverless
const connectToDatabase = async () => {
  if (isConnected && mongoose.connection.readyState === 1) {
    console.log('Using existing database connection');
    return mongoose.connection;
  }

  if (connectionPromise) {
    console.log('Waiting for existing connection promise');
    return connectionPromise;
  }

  console.log('Creating new database connection');

  connectionPromise = mongoose.connect(process.env.MONGO_URI, {
    maxPoolSize: 10, // Maintain up to 10 socket connections
    serverSelectionTimeoutMS: 5000, // Keep trying to send operations for 5 seconds
    socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
    bufferCommands: false, // Disable mongoose buffering
    bufferMaxEntries: 0, // Disable mongoose buffering
  });

  try {
    await connectionPromise;
    isConnected = true;
    console.log('MongoDB connected successfully');

    // Handle connection events
    mongoose.connection.on('disconnected', () => {
      console.log('MongoDB disconnected');
      isConnected = false;
      connectionPromise = null;
    });

    mongoose.connection.on('error', (err) => {
      console.error('MongoDB connection error:', err);
      isConnected = false;
      connectionPromise = null;
    });

    return mongoose.connection;
  } catch (error) {
    console.error('MongoDB connection failed:', error);
    isConnected = false;
    connectionPromise = null;
    throw error;
  }
};

// Import modules with error handling
let swaggerUi, swaggerSpecs, connectRedis, connectGridFS;
let authRoutes, userRoutes, productRouter, orderRouter, contactRoutes, imageRoutes, videoBlogRoutes;

try {
  swaggerUi = require("swagger-ui-express");
  swaggerSpecs = require("../config/swagger");
  ({ connectRedis } = require("../config/redis"));
  ({ connectGridFS } = require("../config/gridfs"));
  authRoutes = require("../routes/authRoutes");
  userRoutes = require("../routes/userRoutes");
  productRouter = require("../routes/gridfsProductRoutes");
  orderRouter = require("../routes/orderRoutes");
  contactRoutes = require("../routes/contactRoutes");
  imageRoutes = require("../routes/imageRoutes");
  videoBlogRoutes = require("../routes/videoBlogRoutes");
} catch (importError) {
  console.error('Error importing modules:', importError);
  console.error('Import error stack:', importError.stack);
}

const app = express();

// Global connection state
let redisConnected = false;
let gridfsConnected = false;

// Initialize connections with error handling
const initializeConnections = async () => {
  try {
    // Connect to MongoDB first
    await connectToDatabase();

    // Try to connect to Redis (non-blocking)
    if (connectRedis) {
      try {
        await connectRedis();
        redisConnected = true;
        console.log('Redis connected successfully');
      } catch (redisError) {
        console.error('Redis connection failed:', redisError);
        console.log('Continuing without Redis caching...');
        redisConnected = false;
      }
    }

    // Try to connect to GridFS (non-blocking)
    if (connectGridFS && isConnected) {
      try {
        await connectGridFS();
        gridfsConnected = true;
        console.log('GridFS connected successfully');
      } catch (gridfsError) {
        console.error('GridFS connection failed:', gridfsError);
        console.log('Continuing without GridFS...');
        gridfsConnected = false;
      }
    }
  } catch (error) {
    console.error('Failed to initialize connections:', error);
    throw error;
  }
};

// CORS configuration
app.use(
  cors({
    origin: "*",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Swagger API Documentation (only if swagger modules loaded successfully)
if (swaggerUi && swaggerSpecs) {
  app.use(
    "/api-docs",
    swaggerUi.serve,
    swaggerUi.setup(swaggerSpecs, { explorer: true })
  );
}

// Root route - health check
app.get("/", (_, res) => {
  res.json({
    message: "Chinioti Wooden Art API is Healthy",
    documentation: `${process.env.VERCEL_URL || process.env.DEPLOYED_URL || 'http://localhost:5002'}/api-docs`,
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: "1.0.0",
    connections: {
      database: isConnected,
      redis: redisConnected,
      gridfs: gridfsConnected
    }
  });
});

// Auth callback fallback route
app.get("/auth-callback", (req, res) => {
  const { token, error, errorMessage } = req.query;

  if (error) {
    res.status(400).json({
      status: "error",
      message: errorMessage || "Authentication failed",
    });
  } else if (token) {
    res.json({
      status: "success",
      message: "Authentication successful",
      token,
      note: "This is the backend auth-callback endpoint. You should be redirected to the frontend.",
    });
  } else {
    res.status(400).json({
      status: "error",
      message: "Invalid auth callback request",
    });
  }
});

// API Routes (only if modules loaded successfully)
if (authRoutes) app.use("/api/auth", authRoutes);
if (userRoutes) app.use("/api/users", userRoutes);
if (productRouter) app.use("/api/products", productRouter);
if (orderRouter) app.use("/api/orders", orderRouter);
if (contactRoutes) app.use("/api/contact", contactRoutes);
if (imageRoutes) app.use("/api/images", imageRoutes);
if (videoBlogRoutes) app.use("/api/video-blogs", videoBlogRoutes);

// Handle 404
app.use("*", (req, res) => {
  res.status(404).json({
    status: "error",
    message: "Route not found",
    path: req.originalUrl,
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error("Express error:", error);

  if (!res.headersSent) {
    res.status(500).json({
      status: "error",
      message: "Internal server error",
      error: process.env.NODE_ENV === "development" ? error.message : "Something went wrong",
    });
  }
});

// Serverless function handler
module.exports = async (req, res) => {
  // Set CORS headers for all requests
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    console.log('Serverless function invoked:', req.method, req.url);
    await initializeConnections();
    return app(req, res);
  } catch (error) {
    console.error("Serverless function error:", error);
    console.error("Error stack:", error.stack);

    // Ensure we respond with proper JSON
    if (!res.headersSent) {
      return res.status(500).json({
        status: "error",
        message: "Internal server error",
        error: process.env.NODE_ENV === "development" ? error.message : "Something went wrong",
      });
    }
  }
};

// For local development
if (process.env.NODE_ENV !== "production") {
  const PORT = process.env.PORT || 5002;
  app.listen(PORT, async () => {
    await initializeConnections();
    console.log(`Server is running on http://localhost:${PORT}`);
  });
}