const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");

// Load environment variables first
dotenv.config({ path: path.resolve(__dirname, '../.env') });

// Validate required environment variables
const requiredEnvVars = ['MONGO_URI', 'JWT_SECRET'];
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars);
  console.error('Available environment variables:', Object.keys(process.env).filter(key => key.includes('MONGO') || key.includes('JWT')));
}

console.log('Environment check:', {
  NODE_ENV: process.env.NODE_ENV,
  MONGO_URI_EXISTS: !!process.env.MONGO_URI,
  JWT_SECRET_EXISTS: !!process.env.JWT_SECRET,
  REDIS_URL_EXISTS: !!process.env.REDIS_URL,
});

// Import modules with error handling
let swaggerUi, swaggerSpecs, connectDB, connectRedis, connectGridFS;
let authRoutes, userRoutes, productRouter, orderRouter, contactRoutes, imageRoutes, videoBlogRoutes;

try {
  swaggerUi = require("swagger-ui-express");
  swaggerSpecs = require("../config/swagger");
  connectDB = require("../config/db");
  ({ connectRedis } = require("../config/redis"));
  ({ connectGridFS } = require("../config/gridfs"));
  authRoutes = require("../routes/authRoutes");
  userRoutes = require("../routes/userRoutes");
  productRouter = require("../routes/gridfsProductRoutes");
  orderRouter = require("../routes/orderRoutes");
  contactRoutes = require("../routes/contactRoutes");
  imageRoutes = require("../routes/imageRoutes");
  videoBlogRoutes = require("../routes/videoBlogRoutes");
} catch (importError) {
  console.error('Error importing modules:', importError);
  console.error('Import error stack:', importError.stack);
}

const app = express();

// Global connection state
let isConnected = false;

async function connectToDatabase() {
  if (isConnected) {
    console.log("Using existing database connection");
    return;
  }

  try {
    console.log("Establishing new database connections...");
    
    if (!connectDB) {
      throw new Error("connectDB function not available");
    }
    
    await connectDB();
    
    // Connect to GridFS after MongoDB connection is established
    if (connectGridFS) {
      try {
        await connectGridFS();
        console.log("GridFS connected successfully");
      } catch (err) {
        console.error("Failed to connect to GridFS:", err.message);
        console.log("Continuing without GridFS...");
      }
    } else {
      console.warn("GridFS connection function not available");
    }

    // Connect to Redis
    if (connectRedis) {
      try {
        await connectRedis();
        console.log("Redis connected successfully");
      } catch (err) {
        console.error("Failed to connect to Redis:", err.message);
        console.log("Continuing without Redis caching...");
      }
    } else {
      console.warn("Redis connection function not available");
    }

    isConnected = true;
    console.log("Database connections established successfully");
  } catch (error) {
    console.error("Database connection failed:", error);
    console.error("Error stack:", error.stack);
    console.error("MongoDB URI exists:", !!process.env.MONGO_URI);
    console.error("Environment:", process.env.NODE_ENV);
    throw new Error(`Database connection failed: ${error.message}`);
  }
}

// CORS configuration
app.use(
  cors({
    origin: process.env.NODE_ENV === "production" 
      ? [process.env.FRONTEND_URL, process.env.DEPLOYED_URL].filter(Boolean)
      : "*",
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// Swagger API Documentation
if (swaggerUi && swaggerSpecs) {
  app.use(
    "/api-docs",
    swaggerUi.serve,
    swaggerUi.setup(swaggerSpecs, { explorer: true })
  );
} else {
  console.warn("Swagger documentation not available");
}

// Root route - health check
app.get("/", (_, res) => {
  res.json({
    message: "Chinioti Wooden Art API is Healthy",
    documentation: `${process.env.VERCEL_URL || process.env.DEPLOYED_URL || 'http://localhost:5002'}/api-docs`,
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV,
    version: "1.0.0"
  });
});

// Auth callback fallback route
app.get("/auth-callback", (req, res) => {
  const { token, error, errorMessage } = req.query;

  if (error) {
    res.status(400).json({
      status: "error",
      message: errorMessage || "Authentication failed",
    });
  } else if (token) {
    res.json({
      status: "success",
      message: "Authentication successful",
      token,
      note: "This is the backend auth-callback endpoint. You should be redirected to the frontend.",
    });
  } else {
    res.status(400).json({
      status: "error",
      message: "Invalid auth callback request",
    });
  }
});

// API Routes
if (authRoutes) app.use("/api/auth", authRoutes);
if (userRoutes) app.use("/api/users", userRoutes);
if (productRouter) app.use("/api/products", productRouter);
if (orderRouter) app.use("/api/orders", orderRouter);
if (contactRoutes) app.use("/api/contact", contactRoutes);
if (imageRoutes) app.use("/api/images", imageRoutes);
if (videoBlogRoutes) app.use("/api/video-blogs", videoBlogRoutes);

// Handle 404
app.use("*", (req, res) => {
  res.status(404).json({
    status: "error",
    message: "Route not found",
    path: req.originalUrl,
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error("Error:", err);
  res.status(err.status || 500).json({
    status: "error",
    message: err.message || "Internal server error",
    ...(process.env.NODE_ENV === "development" && { stack: err.stack }),
  });
});

// Serverless function handler
module.exports = async (req, res) => {
  // Set CORS headers for all requests
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  try {
    console.log('Serverless function invoked:', req.method, req.url);
    await connectToDatabase();
    return app(req, res);
  } catch (error) {
    console.error("Serverless function error:", error);
    console.error("Error stack:", error.stack);
    
    // Ensure we respond with proper JSON
    if (!res.headersSent) {
      return res.status(500).json({
        status: "error",
        message: "Internal server error",
        error: process.env.NODE_ENV === "development" ? error.message : "Something went wrong",
      });
    }
  }
};

// For local development
if (process.env.NODE_ENV !== "production") {
  const PORT = process.env.PORT || 5002;
  app.listen(PORT, async () => {
    await connectToDatabase();
    console.log(`Server is running on http://localhost:${PORT}`);
  });
}
