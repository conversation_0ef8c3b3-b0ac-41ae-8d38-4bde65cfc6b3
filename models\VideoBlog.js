const mongoose = require('mongoose');

const videoBlogSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Video title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  videoFileId: {
    type: mongoose.Schema.Types.ObjectId,
    required: [true, 'Video file is required'],
    ref: 'fs.files' // Reference to GridFS files collection
  },
  videoFilename: {
    type: String,
    required: [true, 'Video filename is required']
  },
  mimetype: {
    type: String,
    required: true
  },
  fileSize: {
    type: Number,
    required: true
  },
  duration: {
    type: Number, // Duration in seconds
    default: null
  },
  thumbnailFileId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'fs.files', // Reference to GridFS files collection for thumbnail
    default: null
  },
  thumbnailFilename: {
    type: String,
    default: null
  },
  isActive: {
    type: Boolean,
    default: true
  },
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  views: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Index for better query performance
videoBlogSchema.index({ createdAt: -1 });
videoBlogSchema.index({ isActive: 1 });
videoBlogSchema.index({ title: 'text' });

// Virtual for video URL
videoBlogSchema.virtual('videoUrl').get(function() {
  return `/api/video-blogs/stream/${this.videoFileId}`;
});

// Virtual for thumbnail URL
videoBlogSchema.virtual('thumbnailUrl').get(function() {
  if (this.thumbnailFileId) {
    return `/api/video-blogs/thumbnail/${this.thumbnailFileId}`;
  }
  return null;
});

// Ensure virtual fields are serialized
videoBlogSchema.set('toJSON', { virtuals: true });
videoBlogSchema.set('toObject', { virtuals: true });

// Pre-save middleware to update the updatedAt field
videoBlogSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('VideoBlog', videoBlogSchema);
