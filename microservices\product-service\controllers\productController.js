const Product = require('../models/Product');
const { clearCache } = require('../config/redis');

// Create a new product
exports.createProduct = async (req, res) => {
    try {
        const product = await Product.create(req.body);

        // Invalidate product cache
        await clearCache('api:/api/products*');

        res.status(201).json({
            status: 'success',
            data: {
                product
            }
        });
    } catch (err) {
        console.error('Create product error:', err);
        res.status(400).json({
            status: 'fail',
            message: err.message
        });
    }
};

// Get all products
exports.getAllProducts = async (req, res) => {
    try {
        // Build query
        const queryObj = { ...req.query };
        const excludedFields = ['page', 'sort', 'limit', 'fields'];
        excludedFields.forEach(field => delete queryObj[field]);

        // Advanced filtering
        let queryStr = JSON.stringify(queryObj);
        queryStr = queryStr.replace(/\b(gte|gt|lte|lt)\b/g, match => `$${match}`);

        let query = Product.find(JSON.parse(queryStr));

        // Sorting
        if (req.query.sort) {
            const sortBy = req.query.sort.split(',').join(' ');
            query = query.sort(sortBy);
        } else {
            query = query.sort('-createdAt');
        }

        // Field limiting
        if (req.query.fields) {
            const fields = req.query.fields.split(',').join(' ');
            query = query.select(fields);
        } else {
            query = query.select('-__v');
        }

        // Pagination
        const page = parseInt(req.query.page, 10) || 1;
        const limit = parseInt(req.query.limit, 10) || 100;
        const skip = (page - 1) * limit;
        query = query.skip(skip).limit(limit);

        // Execute query
        const products = await query;

        res.status(200).json({
            status: 'success',
            results: products.length,
            data: {
                products
            }
        });
    } catch (err) {
        console.error('Get all products error:', err);
        res.status(404).json({
            status: 'fail',
            message: err.message
        });
    }
};

// Get a single product
exports.getProduct = async (req, res) => {
    try {
        const id = req.params.id;
        const product = await Product.findById(id);

        if (!product) {
            return res.status(404).json({
                status: 'fail',
                message: 'Product not found'
            });
        }

        res.status(200).json({
            status: 'success',
            data: {
                product
            }
        });
    } catch (err) {
        console.error('Get product error:', err);
        res.status(404).json({
            status: 'fail',
            message: err.message
        });
    }
};

// Update a product
exports.updateProduct = async (req, res) => {
    try {
        const id = req.params.id;
        const product = await Product.findByIdAndUpdate(id, req.body, {
            new: true,
            runValidators: true
        });

        if (!product) {
            return res.status(404).json({
                status: 'fail',
                message: 'Product not found'
            });
        }

        // Invalidate both the specific product cache and the all products cache
        await Promise.all([
            clearCache(`api:/api/products/${id}`),
            clearCache('api:/api/products*')
        ]);

        res.status(200).json({
            status: 'success',
            data: {
                product
            }
        });
    } catch (err) {
        console.error('Update product error:', err);
        res.status(404).json({
            status: 'fail',
            message: err.message
        });
    }
};

// Delete a product
exports.deleteProduct = async (req, res) => {
    try {
        const id = req.params.id;
        const product = await Product.findByIdAndDelete(id);

        if (!product) {
            return res.status(404).json({
                status: 'fail',
                message: 'Product not found'
            });
        }

        // Invalidate both the specific product cache and the all products cache
        await Promise.all([
            clearCache(`api:/api/products/${id}`),
            clearCache('api:/api/products*')
        ]);

        res.status(204).json({
            status: 'success',
            data: null
        });
    } catch (err) {
        console.error('Delete product error:', err);
        res.status(404).json({
            status: 'fail',
            message: err.message
        });
    }
};
