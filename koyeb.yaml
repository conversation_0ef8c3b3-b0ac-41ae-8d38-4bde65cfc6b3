name: chinioti-wooden-art
services:
  - name: server
    type: docker
    docker:
      image: $KOYEB_DOCKER_IMAGE
    ports:
      - port: 5002
        protocol: http
    env:
      - name: PORT
        value: "5002"
      - name: MONGO_URI
        secret: MONGO_URI
      - name: JWT_SECRET
        secret: JWT_SECRET
      - name: GO<PERSON><PERSON><PERSON>_CLIENT_ID
        secret: GOOGLE_CLIENT_ID
      - name: GO<PERSON><PERSON>LE_CLIENT_SECRET
        secret: GOOGLE_CLIENT_SECRET
      - name: EMAIL_USER
        secret: EMAIL_USER
      - name: EMAIL_PASSWORD
        secret: EMAIL_PASSWORD
      - name: GO<PERSON><PERSON><PERSON>_REDIRECT_URI
        secret: GOOGLE_REDIRECT_URI
      - name: REDIS_URL
        secret: REDIS_URL
      - name: DEPLOYED_URL
        secret: DEPLOYED_URL
    regions:
      - fra
    instance_type: free
    scaling:
      min: 1
      max: 1
    routes:
      - path: /
        port: 5002
# Using Upstash Redis cloud service instead of running a Redis container
