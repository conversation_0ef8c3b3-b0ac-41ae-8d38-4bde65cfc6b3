const userController = require('../controllers/userController');
const express = require('express');
const router = express.Router();
const { cacheMiddleware } = require('../config/redis');

const app = require('express')();

router.get('/:id', cacheMiddleware(300), userController.getUser);
router.get('/', cacheMiddleware(300), userController.getUsers);
router.put('/:id', userController.updateUser);
router.delete('/:id', userController.deleteUser);

module.exports = router;