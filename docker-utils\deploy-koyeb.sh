#!/bin/bash

# Script to deploy the application to Koyeb (Free Tier)

echo "Preparing to deploy to Koyeb Free Tier..."

# Check if Koyeb CLI is installed
if ! command -v koyeb &> /dev/null; then
    echo "Koyeb CLI not found. Installing..."
    curl -fsSL https://cli.koyeb.com/install.sh | sh
fi

# Check if user is logged in to Koyeb
if ! koyeb whoami &> /dev/null; then
    echo "Please log in to Koyeb:"
    koyeb login
fi

# Create secrets from .env file
echo "Creating secrets from .env file..."
if [ -f .env ]; then
    # Read .env file and create secrets
    while IFS='=' read -r key value || [[ -n "$key" ]]; do
        # Skip empty lines or comments
        if [[ -z "$key" || "$key" == \#* ]]; then
            continue
        fi

        # Trim whitespace
        key=$(echo "$key" | xargs)
        value=$(echo "$value" | xargs)

        # Create secret if it doesn't exist
        if ! koyeb secret get "$key" &> /dev/null; then
            echo "Creating secret: $key"
            koyeb secret create "$key" --value "$value"
        else
            echo "Secret $key already exists"
        fi
    done < .env

    # Add DEPLOYED_URL secret if it doesn't exist
    if ! koyeb secret get "DEPLOYED_URL" &> /dev/null; then
        echo "Creating secret: DEPLOYED_URL"
        koyeb secret create "DEPLOYED_URL" --value "https://chinioti-wooden-art-server.koyeb.app"
    else
        echo "Secret DEPLOYED_URL already exists"
    fi
else
    echo "Error: .env file not found!"
    exit 1
fi

# Build and push Docker image to a registry
echo "Would you like to build and push the Docker image to Docker Hub? (y/n)"
read -r build_push

if [[ "$build_push" =~ ^[Yy]$ ]]; then
    echo "Enter your Docker Hub username:"
    read -r docker_username

    echo "Building Docker image..."
    docker build -t "$docker_username/chinioti-server:latest" .

    echo "Logging in to Docker Hub..."
    docker login

    echo "Pushing image to Docker Hub..."
    docker push "$docker_username/chinioti-server:latest"

    # Update koyeb.yaml with the Docker image
    sed -i "s|\$KOYEB_DOCKER_IMAGE|$docker_username/chinioti-server:latest|g" koyeb.yaml
fi

# Deploy the application
echo "Deploying application to Koyeb..."
koyeb app create chinioti-wooden-art --config koyeb.yaml

echo "Deployment initiated!"
echo "You can check the status with: koyeb service get chinioti-wooden-art-server"
echo "Once deployed, your application will be available at: https://chinioti-wooden-art-server.koyeb.app"
echo ""
echo "Note: Since you're using the free tier, Redis data will not be persistent."
echo "If the Redis container restarts, all cached data will be lost."
