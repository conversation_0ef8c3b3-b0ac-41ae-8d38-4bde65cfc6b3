/**
 * Health Check Endpoint for Vercel Serverless Function
 * This endpoint provides a simple health check without heavy dependencies
 * to verify that the deployment is working correctly.
 */

module.exports = async (req, res) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS');
  res.setHeader(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization'
  );

  // Handle preflight requests
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({
      status: 'error',
      message: 'Method not allowed. Use GET request.',
      allowedMethods: ['GET']
    });
  }

  try {
    const healthData = {
      status: 'success',
      message: 'Deployment successful - Chinioti Wooden Art API is running',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0',
      uptime: process.uptime(),
      deployment: {
        platform: 'Vercel',
        region: process.env.VERCEL_REGION || 'unknown',
        url: process.env.VERCEL_URL || process.env.DEPLOYED_URL || 'localhost'
      },
      services: {
        api: 'healthy',
        database: 'not_checked', // We don't check DB in health endpoint to keep it lightweight
        redis: 'not_checked'     // We don't check Redis in health endpoint to keep it lightweight
      }
    };

    // Return successful health check
    res.status(200).json(healthData);
  } catch (error) {
    console.error('Health check error:', error);
    
    // Return error response
    res.status(500).json({
      status: 'error',
      message: 'Health check failed',
      timestamp: new Date().toISOString(),
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
};
