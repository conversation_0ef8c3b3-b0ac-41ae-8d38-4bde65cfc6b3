#!/bin/bash

# <PERSON><PERSON>t to stop and clean up Docker containers for the server application

echo "Stopping Docker containers for Chinioti Wooden Art server..."

# Navigate to the server directory (in case script is run from elsewhere)
cd "$(dirname "$0")"

# Check if docker-compose is available
if command -v docker-compose &> /dev/null; then
  echo "Using docker-compose..."
  
  # Ask if user wants to remove volumes
  read -p "Do you want to remove volumes as well? (y/n): " remove_volumes
  
  if [[ "$remove_volumes" =~ ^[Yy]$ ]]; then
    echo "Stopping containers and removing volumes..."
    docker-compose down -v
  else
    echo "Stopping containers..."
    docker-compose down
  fi
  
elif command -v docker &> /dev/null; then
  echo "Using docker compose v2..."
  
  # Ask if user wants to remove volumes
  read -p "Do you want to remove volumes as well? (y/n): " remove_volumes
  
  if [[ "$remove_volumes" =~ ^[Yy]$ ]]; then
    echo "Stopping containers and removing volumes..."
    docker compose down -v
  else
    echo "Stopping containers..."
    docker compose down
  fi
  
else
  echo "Error: Neither docker-compose nor docker compose is available."
  echo "Please install Dock<PERSON> and <PERSON><PERSON> Compose before running this script."
  exit 1
fi

echo "Containers stopped successfully!"
