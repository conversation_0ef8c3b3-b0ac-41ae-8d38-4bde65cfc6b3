/**
 * Test script for Video Blog API endpoints
 * This script tests the video blog functionality without requiring actual video files
 */

const axios = require('axios');
require('dotenv').config();

const API_BASE_URL = process.env.DEPLOYED_URL || 'http://localhost:5002';

// Test configuration
const testConfig = {
  baseURL: `${API_BASE_URL}/api/video-blogs`,
  timeout: 10000
};

console.log('🎬 Testing Video Blog API...');
console.log(`Base URL: ${testConfig.baseURL}`);

// Test 1: Get all video blogs (should work without authentication)
async function testGetAllVideoBlogs() {
  try {
    console.log('\n📋 Test 1: Get all video blogs');
    const response = await axios.get(testConfig.baseURL, { timeout: testConfig.timeout });
    
    console.log('✅ Status:', response.status);
    console.log('✅ Response structure:', {
      status: response.data.status,
      hasVideoBlogs: Array.isArray(response.data.data?.videoBlogs),
      videoBlogCount: response.data.data?.videoBlogs?.length || 0,
      hasPagination: !!response.data.data?.pagination
    });
    
    return true;
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
    return false;
  }
}

// Test 2: Get video blogs with pagination
async function testGetVideoBlogsWithPagination() {
  try {
    console.log('\n📄 Test 2: Get video blogs with pagination');
    const response = await axios.get(`${testConfig.baseURL}?page=1&limit=5`, { 
      timeout: testConfig.timeout 
    });
    
    console.log('✅ Status:', response.status);
    console.log('✅ Pagination info:', response.data.data?.pagination);
    
    return true;
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
    return false;
  }
}

// Test 3: Search video blogs
async function testSearchVideoBlogs() {
  try {
    console.log('\n🔍 Test 3: Search video blogs');
    const response = await axios.get(`${testConfig.baseURL}?search=wood`, { 
      timeout: testConfig.timeout 
    });
    
    console.log('✅ Status:', response.status);
    console.log('✅ Search results:', {
      totalItems: response.data.data?.pagination?.totalItems || 0,
      foundVideos: response.data.data?.videoBlogs?.length || 0
    });
    
    return true;
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
    return false;
  }
}

// Test 4: Get single video blog (will fail if no videos exist, but tests the endpoint)
async function testGetSingleVideoBlog() {
  try {
    console.log('\n🎯 Test 4: Get single video blog');
    
    // First get all videos to find a valid ID
    const allVideosResponse = await axios.get(testConfig.baseURL, { timeout: testConfig.timeout });
    const videoBlogs = allVideosResponse.data.data?.videoBlogs || [];
    
    if (videoBlogs.length === 0) {
      console.log('⚠️  No video blogs found to test single video retrieval');
      return true;
    }
    
    const firstVideoId = videoBlogs[0].id || videoBlogs[0]._id;
    const response = await axios.get(`${testConfig.baseURL}/${firstVideoId}`, { 
      timeout: testConfig.timeout 
    });
    
    console.log('✅ Status:', response.status);
    console.log('✅ Video blog retrieved:', {
      id: response.data.data?.videoBlog?.id,
      title: response.data.data?.videoBlog?.title,
      views: response.data.data?.videoBlog?.views
    });
    
    return true;
  } catch (error) {
    console.log('❌ Error:', error.response?.data?.message || error.message);
    return false;
  }
}

// Test 5: Test invalid video blog ID
async function testInvalidVideoBlogId() {
  try {
    console.log('\n❌ Test 5: Test invalid video blog ID');
    const invalidId = '507f1f77bcf86cd799439011'; // Valid ObjectId format but non-existent
    
    await axios.get(`${testConfig.baseURL}/${invalidId}`, { timeout: testConfig.timeout });
    console.log('❌ Expected 404 error but got success');
    return false;
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ Correctly returned 404 for invalid ID');
      return true;
    } else {
      console.log('❌ Unexpected error:', error.response?.data?.message || error.message);
      return false;
    }
  }
}

// Test 6: Test video streaming endpoint structure
async function testVideoStreamingEndpoint() {
  try {
    console.log('\n🎥 Test 6: Test video streaming endpoint structure');
    const invalidFileId = '507f1f77bcf86cd799439011'; // Valid ObjectId format
    
    await axios.get(`${testConfig.baseURL}/stream/${invalidFileId}`, { timeout: testConfig.timeout });
    console.log('❌ Expected 404 error but got success');
    return false;
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ Video streaming endpoint is properly configured (404 for non-existent file)');
      return true;
    } else {
      console.log('❌ Unexpected error:', error.response?.data?.message || error.message);
      return false;
    }
  }
}

// Test 7: Test thumbnail endpoint structure
async function testThumbnailEndpoint() {
  try {
    console.log('\n🖼️  Test 7: Test thumbnail endpoint structure');
    const invalidFileId = '507f1f77bcf86cd799439011'; // Valid ObjectId format
    
    await axios.get(`${testConfig.baseURL}/thumbnail/${invalidFileId}`, { timeout: testConfig.timeout });
    console.log('❌ Expected 404 error but got success');
    return false;
  } catch (error) {
    if (error.response?.status === 404) {
      console.log('✅ Thumbnail endpoint is properly configured (404 for non-existent file)');
      return true;
    } else {
      console.log('❌ Unexpected error:', error.response?.data?.message || error.message);
      return false;
    }
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting Video Blog API Tests...\n');
  
  const tests = [
    { name: 'Get All Video Blogs', fn: testGetAllVideoBlogs },
    { name: 'Get Video Blogs with Pagination', fn: testGetVideoBlogsWithPagination },
    { name: 'Search Video Blogs', fn: testSearchVideoBlogs },
    { name: 'Get Single Video Blog', fn: testGetSingleVideoBlog },
    { name: 'Test Invalid Video Blog ID', fn: testInvalidVideoBlogId },
    { name: 'Test Video Streaming Endpoint', fn: testVideoStreamingEndpoint },
    { name: 'Test Thumbnail Endpoint', fn: testThumbnailEndpoint }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.log(`❌ Test "${test.name}" threw an error:`, error.message);
      failed++;
    }
  }
  
  console.log('\n📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Video Blog API is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the implementation.');
  }
}

// Run the tests
runAllTests().catch(error => {
  console.error('💥 Test runner failed:', error.message);
  process.exit(1);
});
