const mongoose = require("mongoose");

const productSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
      enum: ['regular', 'featured', 'sale', 'new'],
      default: 'regular'
    },
    price: {
      type: Number,
      required: true,
    },
    image: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    quantity: {
      type: Number,
      required: true,
    },
    stock: {
      type: Boolean,
      required: false,
      default: true
    },
    created: {
      type: Date,
      default: Date.now,
    },
    category: {
      type: String,
      required: true, // Changed to required
    },
    images: {
      type: [String],
      default: [],
    },
    video: {
      type: String,
      required: false,
    },
    discount: {
      type: String,
      required: false,
    },
    id: {
      type: String,
      required: false,
    },
  },
  {
    timestamps: true,
    // Add toJSON option to transform _id to id for better frontend compatibility
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

module.exports = mongoose.model("Product", productSchema);
