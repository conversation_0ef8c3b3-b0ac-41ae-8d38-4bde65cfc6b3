version: '3.8'

services:
  # API Gateway Service
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: api-gateway
    ports:
      - "5002:5002"
    environment:
      - PORT=5002
      - SERVICE_NAME=api-gateway
      - AUTH_SERVICE_URL=http://localhost:4001
      - PRODUCT_SERVICE_URL=http://localhost:4002
      - ORDER_SERVICE_URL=http://localhost:4003
      - CLIENT_URL=http://localhost:3000
      - NODE_ENV=production
    depends_on:
      - auth-service
      - product-service
      - order-service
    network_mode: "host"
    restart: unless-stopped

  # Auth Service
  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    container_name: auth-service
    ports:
      - "4001:4001"
    environment:
      - PORT=4001
      - SERVICE_NAME=auth-service
      - MONGO_URI=mongodb+srv://umairaltaf982:<EMAIL>/chiniotiWoodenArt
      - JWT_SECRET=UmairAltafJWTtoken
      - GOOGLE_CLIENT_ID=591329059196-mlfnk6s1ah0ul2ilhf546d7i4g7960g3.apps.googleusercontent.com
      - GOOGLE_CLIENT_SECRET=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
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASSWORD=dfln pwtx xzsj jybs
      - GOOGLE_REDIRECT_URI=http://localhost:5002/api/auth/google/callback
      - CLIENT_URL=http://localhost:3000
      - NODE_ENV=production
    network_mode: "host"
    restart: unless-stopped

  # Product Service
  product-service:
    build:
      context: ./product-service
      dockerfile: Dockerfile
    container_name: product-service
    ports:
      - "4002:4002"
    environment:
      - PORT=4002
      - SERVICE_NAME=product-service
      - MONGO_URI=mongodb+srv://umairaltaf982:<EMAIL>/chiniotiWoodenArt
      - JWT_SECRET=UmairAltafJWTtoken
      - AUTH_SERVICE_URL=http://localhost:4001
      - CLIENT_URL=http://localhost:3000
      - NODE_ENV=production
      - REDIS_URL=redis://localhost:6379
    depends_on:
      - auth-service
      - redis
    network_mode: "host"
    restart: unless-stopped

  # Order Service
  order-service:
    build:
      context: ./order-service
      dockerfile: Dockerfile
    container_name: order-service
    ports:
      - "4003:4003"
    environment:
      - PORT=4003
      - SERVICE_NAME=order-service
      - MONGO_URI=mongodb+srv://umairaltaf982:<EMAIL>/chiniotiWoodenArt
      - JWT_SECRET=UmairAltafJWTtoken
      - AUTH_SERVICE_URL=http://localhost:4001
      - PRODUCT_SERVICE_URL=http://localhost:4002
      - CLIENT_URL=http://localhost:3000
      - NODE_ENV=production
    depends_on:
      - auth-service
      - product-service
    network_mode: "host"
    restart: unless-stopped

  # Redis Service
  redis:
    image: redis:7-alpine
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    network_mode: "host"
    restart: unless-stopped

volumes:
  redis-data:
