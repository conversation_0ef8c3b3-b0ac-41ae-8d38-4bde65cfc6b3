#!/bin/bash

# <PERSON>ript to build Docker images for all microservices

echo "Building Docker images for all microservices..."

# Navigate to microservices directory
cd "$(dirname "$0")"

# Check if docker-compose is available
if command -v docker-compose &> /dev/null; then
    echo "Using docker-compose..."
    docker-compose build
elif command -v docker &> /dev/null; then
    echo "Using docker compose v2..."
    docker compose build
else
    echo "Error: Neither docker-compose nor docker compose is available."
    echo "Please install Docker and Docker Compose before running this script."
    exit 1
fi

echo "Docker images built successfully!"
echo "To start the services, run: ./docker-run.sh"
