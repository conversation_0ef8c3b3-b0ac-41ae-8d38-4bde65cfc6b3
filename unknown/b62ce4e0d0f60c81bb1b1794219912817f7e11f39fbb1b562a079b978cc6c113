#!/bin/bash

# Script to build and push Docker image to Docker Hub

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Docker Hub Image Push Script${NC}"
echo "This script will build and push your server image to Docker Hub"
echo "--------------------------------------------------------------"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo -e "${RED}Error: Docker is not installed or not in PATH${NC}"
    echo "Please install Docker first: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if user is logged in to Docker Hub
echo -e "${YELLOW}Checking Docker Hub login status...${NC}"
if ! docker info 2>/dev/null | grep -q "Username"; then
    echo -e "${YELLOW}You are not logged in to Docker Hub.${NC}"

    # Check if pass is initialized
    if command -v pass &> /dev/null; then
        if ! pass ls &> /dev/null; then
            echo -e "${YELLOW}The 'pass' password manager is not initialized.${NC}"
            echo -e "${YELLOW}You have two options:${NC}"
            echo "1. Initialize pass (recommended for secure credential storage):"
            echo "   Run: gpg --gen-key"
            echo "   Then: pass init YOUR_GPG_ID"
            echo "2. Use a simpler credential store for this session."

            read -p "Do you want to use a simpler credential store for now? (y/n): " USE_SIMPLE_STORE
            if [[ "$USE_SIMPLE_STORE" == "y" || "$USE_SIMPLE_STORE" == "Y" ]]; then
                # Configure Docker to use a simpler credential store
                mkdir -p ~/.docker
                echo '{ "credsStore": "" }' > ~/.docker/config.json
                echo -e "${GREEN}Using basic credential store for this session.${NC}"
            fi
        fi
    fi

    echo -e "${YELLOW}Please login to Docker Hub:${NC}"
    docker login

    # Check if login was successful
    if [ $? -ne 0 ]; then
        echo -e "${RED}Docker Hub login failed.${NC}"
        echo -e "${YELLOW}Alternative login methods:${NC}"
        echo "1. Try logging in with username and password in one command:"
        echo "   docker login -u YOUR_USERNAME"
        echo "2. Set credential store to use keychain:"
        echo "   echo '{ \"credsStore\": \"\" }' > ~/.docker/config.json"
        echo "   Then try docker login again"
        exit 1
    else
        echo -e "${GREEN}Login was successful!${NC}"
    fi
fi

# Get Docker Hub username
read -p "Enter your Docker Hub username: " DOCKER_USERNAME

# Validate username
if [ -z "$DOCKER_USERNAME" ]; then
    echo -e "${RED}Error: Docker Hub username cannot be empty${NC}"
    exit 1
fi

# Ask for image name and tag
read -p "Enter image name [default: chinioti-server]: " IMAGE_NAME
IMAGE_NAME=${IMAGE_NAME:-chinioti-server}

read -p "Enter image tag [default: latest]: " IMAGE_TAG
IMAGE_TAG=${IMAGE_TAG:-latest}

# Full image name
FULL_IMAGE_NAME="$DOCKER_USERNAME/$IMAGE_NAME:$IMAGE_TAG"

echo -e "${YELLOW}Building Docker image: ${FULL_IMAGE_NAME}${NC}"
echo "This may take a few minutes..."

# Build the Docker image
docker build -t "$FULL_IMAGE_NAME" .

# Check if build was successful
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Docker build failed${NC}"
    exit 1
fi

echo -e "${GREEN}Docker image built successfully!${NC}"
echo -e "${YELLOW}Pushing image to Docker Hub: ${FULL_IMAGE_NAME}${NC}"

# Push the image to Docker Hub
docker push "$FULL_IMAGE_NAME"

# Check if push was successful
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: Failed to push image to Docker Hub${NC}"
    exit 1
fi

echo -e "${GREEN}Image successfully pushed to Docker Hub!${NC}"
echo "--------------------------------------------------------------"
echo -e "Image: ${YELLOW}${FULL_IMAGE_NAME}${NC}"
echo -e "${GREEN}You can now use this image in your Koyeb deployment.${NC}"
echo ""
echo "To update your koyeb.yaml file with this image, run:"
echo -e "${YELLOW}sed -i \"s|\\\$KOYEB_DOCKER_IMAGE|${FULL_IMAGE_NAME}|g\" koyeb.yaml${NC}"
