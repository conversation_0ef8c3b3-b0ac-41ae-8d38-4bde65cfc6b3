const { createClient } = require('redis');

// Create Redis client with configuration
const redisClient = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379',
  socket: {
    reconnectStrategy: (retries) => {
      // Exponential backoff: 2^retries * 100ms (max 10 seconds)
      const delay = Math.min(Math.pow(2, retries) * 100, 10000);
      return delay;
    },
  },
});

// Handle Redis client events
redisClient.on('connect', () => {
  console.log('Redis client connected');
});

redisClient.on('error', (err) => {
  console.error('Redis client error:', err);
});

redisClient.on('reconnecting', () => {
  console.log('Redis client reconnecting...');
});

// Connect to Redis
const connectRedis = async () => {
  try {
    await redisClient.connect();
  } catch (error) {
    console.error('Redis connection error:', error);
    // Don't exit process, allow the app to function without Redis
    console.log('Continuing without Redis caching...');
  }
};

// Cache middleware
const cacheMiddleware = (duration) => {
  return async (req, res, next) => {
    // Skip caching for non-GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Create a unique cache key based on the request URL and query parameters
    const cacheKey = `api:${req.originalUrl || req.url}`;

    try {
      // Check if Redis client is connected
      if (!redisClient.isReady) {
        return next();
      }

      // Try to get cached response
      const cachedResponse = await redisClient.get(cacheKey);

      if (cachedResponse) {
        // If cache hit, send cached response
        console.log(`Cache hit for ${cacheKey}`);
        const parsedResponse = JSON.parse(cachedResponse);
        return res.status(200).json(parsedResponse);
      }

      // If cache miss, continue to the controller
      console.log(`Cache miss for ${cacheKey}`);

      // Store the original res.json method
      const originalJson = res.json;

      // Override res.json method to cache the response before sending
      res.json = function (data) {
        // Only cache successful responses
        if (res.statusCode === 200) {
          // Cache the response
          redisClient.setEx(cacheKey, duration, JSON.stringify(data))
            .catch(err => console.error(`Error caching response for ${cacheKey}:`, err));
        }

        // Call the original json method
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      console.error(`Cache middleware error for ${cacheKey}:`, error);
      next();
    }
  };
};

// Clear cache by pattern
const clearCache = async (pattern) => {
  try {
    if (!redisClient.isReady) {
      console.log('Redis not connected, skipping cache clear');
      return;
    }

    const keys = await redisClient.keys(pattern);
    if (keys.length > 0) {
      console.log(`Clearing ${keys.length} cache entries matching pattern: ${pattern}`);
      await Promise.all(keys.map(key => redisClient.del(key)));
    }
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
};

module.exports = {
  redisClient,
  connectRedis,
  cacheMiddleware,
  clearCache
};
