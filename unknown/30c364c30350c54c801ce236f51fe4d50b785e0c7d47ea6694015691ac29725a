const express = require('express');
const router = express.Router();
const productController = require('../controllers/productController');
const { protect } = require('../middleware/authMiddleware');
const { cacheMiddleware } = require('../config/redis');

// Public routes - with Redis caching (cache for 5 minutes = 300 seconds)
router.get('/', cacheMiddleware(300), productController.getAllProducts);
router.get('/:id', cacheMiddleware(300), productController.getProduct);

// Protected routes (admin only)
router.use(protect);
router.post('/', productController.createProduct);
router.patch('/:id', productController.updateProduct);
router.delete('/:id', productController.deleteProduct);

module.exports = router;
