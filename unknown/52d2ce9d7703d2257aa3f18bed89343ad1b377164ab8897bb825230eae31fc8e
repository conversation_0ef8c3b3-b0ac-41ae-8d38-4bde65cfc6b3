# Chinioti Wooden Art Microservices Architecture

This document provides an overview of the microservices architecture implemented for the Chinioti Wooden Art application.

## Architecture Overview

The application is divided into the following microservices:

1. **API Gateway** - Entry point for all client requests
2. **Auth Service** - Handles user authentication and management
3. **Product Service** - Manages product data and operations
4. **Order Service** - Handles order processing and management

## Service Details

### API Gateway (Port 5002)

- Acts as a single entry point for all client requests
- Routes requests to appropriate microservices
- Handles CORS and basic error handling
- Provides a unified API for the frontend

### Auth Service (Port 4001)

- Handles user registration and login
- Manages user profiles
- Provides JWT authentication
- Handles password reset functionality
- Supports Google OAuth authentication

### Product Service (Port 4002)

- Manages product catalog
- Provides product search and filtering
- Handles product CRUD operations
- Supports product categorization

### Order Service (Port 4003)

- Processes customer orders
- Manages order status and history
- Handles order cancellation
- Communicates with Product Service to validate products

## Running the Services

Each service can be started independently using the following commands:

```bash
# Start API Gateway
cd api-gateway
npm install
npm run dev

# Start Auth Service
cd auth-service
npm install
npm run dev

# Start Product Service
cd product-service
npm install
npm run dev

# Start Order Service
cd order-service
npm install
npm run dev
```

## Environment Variables

Each service requires its own `.env` file with the following variables:

### API Gateway
```
PORT=5002
SERVICE_NAME=api-gateway
JWT_SECRET=UmairAltafJWTtoken
AUTH_SERVICE_URL=http://localhost:4001
PRODUCT_SERVICE_URL=http://localhost:4002
ORDER_SERVICE_URL=http://localhost:4003
CLIENT_URL=http://localhost:3000
```

### Auth Service
```
PORT=4001
SERVICE_NAME=auth-service
MONGO_URI=mongodb+srv://username:<EMAIL>/dbname
JWT_SECRET=UmairAltafJWTtoken
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password
GOOGLE_REDIRECT_URI=http://localhost:5002/api/auth/google/callback
CLIENT_URL=http://localhost:3000
```

### Product Service
```
PORT=4002
SERVICE_NAME=product-service
MONGO_URI=mongodb+srv://username:<EMAIL>/dbname
JWT_SECRET=UmairAltafJWTtoken
AUTH_SERVICE_URL=http://localhost:4001
CLIENT_URL=http://localhost:3000
```

### Order Service
```
PORT=4003
SERVICE_NAME=order-service
MONGO_URI=mongodb+srv://username:<EMAIL>/dbname
JWT_SECRET=UmairAltafJWTtoken
AUTH_SERVICE_URL=http://localhost:4001
PRODUCT_SERVICE_URL=http://localhost:4002
CLIENT_URL=http://localhost:3000
```

## API Documentation

### Auth Service APIs

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login a user
- `GET /api/auth/google` - Initiate Google OAuth login
- `GET /api/auth/google/callback` - Google OAuth callback
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/verify-otp` - Verify OTP for password reset
- `POST /api/auth/reset-password` - Reset password with token
- `PATCH /api/auth/change-password` - Change password (authenticated)
- `GET /api/users/:id` - Get user by ID (authenticated)
- `GET /api/users` - Get all users (authenticated)
- `PUT /api/users/:id` - Update user (authenticated)
- `DELETE /api/users/:id` - Delete user (authenticated)

### Product Service APIs

- `GET /api/products` - Get all products
- `GET /api/products/:id` - Get product by ID
- `POST /api/products` - Create a new product (authenticated)
- `PATCH /api/products/:id` - Update product (authenticated)
- `DELETE /api/products/:id` - Delete product (authenticated)

### Order Service APIs

- `POST /api/orders` - Create a new order (authenticated)
- `GET /api/orders` - Get all orders for the user (authenticated)
- `GET /api/orders/:id` - Get order by ID (authenticated)
- `PATCH /api/orders/:id/cancel` - Cancel an order (authenticated)
- `PATCH /api/orders/:id/status` - Update order status (admin only)
