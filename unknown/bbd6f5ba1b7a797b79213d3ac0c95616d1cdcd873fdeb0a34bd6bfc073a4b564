const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const router = express.Router();

// Define service URLs - Use localhost for development
const AUTH_SERVICE_URL = 'http://localhost:4001';
const PRODUCT_SERVICE_URL = 'http://localhost:4002';
const ORDER_SERVICE_URL = 'http://localhost:4003';

console.log('Auth Service URL:', AUTH_SERVICE_URL);
console.log('Product Service URL:', PRODUCT_SERVICE_URL);
console.log('Order Service URL:', ORDER_SERVICE_URL);

// Auth Service Routes
router.use(
  '/api/auth',
  createProxyMiddleware({
    target: AUTH_SERVICE_URL,
    changeOrigin: true,
    pathRewrite: {
      '^/api/auth': '/api/auth',
    },
    onProxyReq: (proxyReq, req, res) => {
      // Log the request for debugging
      console.log(`Proxying request to Auth Service: ${req.method} ${req.url}`);

      // If the request has a body, properly handle it
      if (req.body) {
        const bodyData = JSON.stringify(req.body);
        proxyReq.setHeader('Content-Type', 'application/json');
        proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
        proxyReq.write(bodyData);
      }
    },
    onProxyRes: (proxyRes, req, res) => {
      // Log the response for debugging
      console.log(`Response from Auth Service: ${proxyRes.statusCode}`);
    },
    onError: (err, req, res) => {
      console.error('Proxy error:', err);
      res.status(500).json({
        status: 'error',
        message: 'Auth service unavailable',
      });
    },
  })
);

router.use(
  '/api/users',
  createProxyMiddleware({
    target: AUTH_SERVICE_URL,
    changeOrigin: true,
    pathRewrite: {
      '^/api/users': '/api/users',
    },
    onProxyReq: (proxyReq, req, res) => {
      // Log the request for debugging
      console.log(
        `Proxying request to Auth Service (User Routes): ${req.method} ${req.url}`
      );

      // If the request has a body, properly handle it
      if (req.body) {
        const bodyData = JSON.stringify(req.body);
        proxyReq.setHeader('Content-Type', 'application/json');
        proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
        proxyReq.write(bodyData);
      }
    },
    onProxyRes: (proxyRes, req, res) => {
      // Log the response for debugging
      console.log(
        `Response from Auth Service (User Routes): ${proxyRes.statusCode}`
      );
    },
    onError: (err, req, res) => {
      console.error('Proxy error (User Routes):', err);
      res.status(500).json({
        status: 'error',
        message: 'User service unavailable',
      });
    },
  })
);

// Product Service Routes
router.use(
  '/api/products',
  createProxyMiddleware({
    target: PRODUCT_SERVICE_URL,
    changeOrigin: true,
    pathRewrite: {
      '^/api/products': '/api/products',
    },
  })
);

// Order Service Routes
router.use(
  '/api/orders',
  createProxyMiddleware({
    target: ORDER_SERVICE_URL,
    changeOrigin: true,
    pathRewrite: {
      '^/api/orders': '/api/orders',
    },
  })
);

module.exports = router;
