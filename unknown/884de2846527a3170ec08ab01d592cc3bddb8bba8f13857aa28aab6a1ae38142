/**
 * Script to migrate existing images from local filesystem to GridFS
 * 
 * This script:
 * 1. Connects to MongoDB and initializes GridFS
 * 2. Reads all products from the database
 * 3. For each product, uploads its images to GridFS
 * 4. Updates the product with the new image filenames
 */
require('dotenv').config();
const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
const { GridFSBucket } = require('mongodb');
const Product = require('../models/Product');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI);
    console.log('MongoDB connected');
    return true;
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Initialize GridFS bucket
let gridFSBucket;

// Connect to GridFS
const connectGridFS = async () => {
  try {
    // Check if MongoDB is connected
    if (mongoose.connection.readyState !== 1) {
      throw new Error('MongoDB not connected');
    }

    // Initialize GridFS bucket
    gridFSBucket = new GridFSBucket(mongoose.connection.db, {
      bucketName: 'uploads'
    });

    console.log('GridFS connected successfully');
    return true;
  } catch (error) {
    console.error('GridFS connection error:', error);
    return false;
  }
};

// Upload a file to GridFS
const uploadFileToGridFS = (filePath, filename, mimetype) => {
  return new Promise((resolve, reject) => {
    try {
      // Check if file exists
      if (!fs.existsSync(filePath)) {
        return reject(new Error(`File not found: ${filePath}`));
      }

      // Create read stream from file
      const readStream = fs.createReadStream(filePath);
      
      // Create write stream to GridFS
      const writeStream = gridFSBucket.openUploadStream(filename, {
        metadata: {
          originalname: path.basename(filePath),
          mimetype: mimetype || 'image/jpeg', // Default to JPEG if mimetype not provided
          uploadDate: new Date()
        }
      });
      
      // Handle errors
      readStream.on('error', (error) => {
        reject(error);
      });
      
      writeStream.on('error', (error) => {
        reject(error);
      });
      
      // Handle completion
      writeStream.on('finish', (file) => {
        resolve(file);
      });
      
      // Pipe read stream to write stream
      readStream.pipe(writeStream);
    } catch (error) {
      reject(error);
    }
  });
};

// Determine mimetype based on file extension
const getMimetype = (filename) => {
  const ext = path.extname(filename).toLowerCase();
  switch (ext) {
    case '.jpg':
    case '.jpeg':
      return 'image/jpeg';
    case '.png':
      return 'image/png';
    case '.gif':
      return 'image/gif';
    case '.webp':
      return 'image/webp';
    default:
      return 'application/octet-stream';
  }
};

// Main migration function
const migrateImages = async () => {
  try {
    // Connect to MongoDB and GridFS
    await connectDB();
    await connectGridFS();
    
    // Get all products
    const products = await Product.find();
    console.log(`Found ${products.length} products to process`);
    
    // Process each product
    for (const product of products) {
      console.log(`Processing product: ${product.name} (${product._id})`);
      
      // Process main image
      if (product.image) {
        const imagePath = path.join(__dirname, '../uploads', product.image);
        try {
          // Upload to GridFS
          const mimetype = getMimetype(product.image);
          const file = await uploadFileToGridFS(imagePath, product.image, mimetype);
          console.log(`Uploaded main image: ${product.image}`);
        } catch (error) {
          console.error(`Error uploading main image for product ${product._id}:`, error.message);
        }
      }
      
      // Process additional images
      if (product.images && product.images.length > 0) {
        for (const imageName of product.images) {
          const imagePath = path.join(__dirname, '../uploads', imageName);
          try {
            // Upload to GridFS
            const mimetype = getMimetype(imageName);
            const file = await uploadFileToGridFS(imagePath, imageName, mimetype);
            console.log(`Uploaded additional image: ${imageName}`);
          } catch (error) {
            console.error(`Error uploading additional image ${imageName} for product ${product._id}:`, error.message);
          }
        }
      }
    }
    
    console.log('Migration completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
};

// Run the migration
migrateImages();
