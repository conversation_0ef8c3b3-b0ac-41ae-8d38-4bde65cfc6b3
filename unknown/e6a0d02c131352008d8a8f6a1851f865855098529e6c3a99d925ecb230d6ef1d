const express = require("express");
const router = express.Router();
const {
  register,
  login,
  googleAuth,
  googleCallback,
  forgotPassword,
  verifyOTP,
  resetPassword,
  changePassword,
} = require("../controllers/authController");
const {
  validateRegistration,
  validateLogin,
} = require("../middleware/authMiddleware");

router.post("/register", validateRegistration, register);
router.post("/login", validateLogin, login);
// Google OAuth routes
router.get("/google", googleAuth);
router.get("/google/callback", googleCallback);

// Password reset routes
router.post("/forgot-password", forgotPassword);
router.post("/verify-otp", verifyOTP);
router.post("/reset-password", resetPassword);
router.patch("/change-password", changePassword);

module.exports = router;
