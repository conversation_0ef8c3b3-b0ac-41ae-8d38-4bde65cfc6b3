const Order = require('../models/Order');
const axios = require('axios');

// Helper function to validate products with the product service
const validateProducts = async (items) => {
    try {
        // Use Docker container name in containerized environment
        const productServiceUrl = process.env.PRODUCT_SERVICE_URL || 'http://localhost:4002';
        console.log(`Validating products with product service at: ${productServiceUrl}`);

        const productPromises = items.map(item =>
            axios.get(`${productServiceUrl}/api/products/${item.productId}`)
        );

        const productResponses = await Promise.all(productPromises);

        // Create a map of product details
        const productMap = {};
        productResponses.forEach(response => {
            const product = response.data.data.product;
            productMap[product._id] = product;
        });

        return productMap;
    } catch (error) {
        console.error('Error validating products:', error);
        throw new Error('Failed to validate products');
    }
};

// Create a new order
exports.createOrder = async (req, res) => {
    try {
        const { items, shippingAddress, paymentMethod, notes } = req.body;
        const userId = req.user.id;

        if (!items || !items.length || !shippingAddress) {
            return res.status(400).json({
                status: 'fail',
                message: 'Order must include items and shipping address'
            });
        }

        // Validate products and get product details
        const productMap = await validateProducts(items);

        // Prepare order items with product details
        const orderItems = items.map(item => ({
            productId: item.productId,
            name: productMap[item.productId].name,
            price: productMap[item.productId].price,
            quantity: item.quantity,
            image: productMap[item.productId].image
        }));

        // Calculate total
        const total = orderItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        // Create order
        const order = await Order.create({
            userId,
            items: orderItems,
            total,
            shippingAddress,
            paymentMethod,
            notes,
            status: 'pending',
            paymentStatus: 'pending'
        });

        res.status(201).json({
            status: 'success',
            data: {
                order
            }
        });
    } catch (error) {
        console.error('Create order error:', error);
        res.status(400).json({
            status: 'fail',
            message: error.message
        });
    }
};

// Get all orders
exports.getAllOrders = async (req, res) => {
    try {
        const userId = req.user.id;
        const { search, status, dateFilter, page = 1, limit = 10 } = req.query;

        // Build query
        let query = { userId }; // Always filter by userId for security

        // Apply search filter if provided
        if (search) {
            // Search in order items for product name or in shipping address
            query.$or = [
                { 'items.name': { $regex: search, $options: 'i' } },
                { 'shippingAddress.name': { $regex: search, $options: 'i' } },
                { 'shippingAddress.address': { $regex: search, $options: 'i' } },
                { 'shippingAddress.city': { $regex: search, $options: 'i' } }
            ];
        }

        // Apply status filter if provided
        if (status) {
            query.status = status;
        }

        // Apply date filter if provided
        if (dateFilter) {
            const now = new Date();
            let dateQuery = {};

            switch (dateFilter) {
                case 'today':
                    // Start of today
                    const startOfToday = new Date(now.setHours(0, 0, 0, 0));
                    dateQuery = { createdAt: { $gte: startOfToday } };
                    break;
                case 'yesterday':
                    // Start of yesterday
                    const startOfYesterday = new Date(now);
                    startOfYesterday.setDate(startOfYesterday.getDate() - 1);
                    startOfYesterday.setHours(0, 0, 0, 0);
                    // End of yesterday
                    const endOfYesterday = new Date(now);
                    endOfYesterday.setDate(endOfYesterday.getDate() - 1);
                    endOfYesterday.setHours(23, 59, 59, 999);
                    dateQuery = {
                        createdAt: {
                            $gte: startOfYesterday,
                            $lte: endOfYesterday
                        }
                    };
                    break;
                case 'last7days':
                    // 7 days ago
                    const last7Days = new Date(now);
                    last7Days.setDate(last7Days.getDate() - 7);
                    dateQuery = { createdAt: { $gte: last7Days } };
                    break;
                case 'last30days':
                    // 30 days ago
                    const last30Days = new Date(now);
                    last30Days.setDate(last30Days.getDate() - 30);
                    dateQuery = { createdAt: { $gte: last30Days } };
                    break;
                default:
                    // No date filter or invalid filter
                    break;
            }

            // Add date query to main query if it's not empty
            if (Object.keys(dateQuery).length > 0) {
                query = { ...query, ...dateQuery };
            }
        }

        // Log the query for debugging
        console.log('Order query:', JSON.stringify(query, null, 2));

        // Pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);
        const pageSize = parseInt(limit);

        // Execute query with pagination
        const orders = await Order.find(query)
            .sort('-createdAt')
            .skip(skip)
            .limit(pageSize);

        // Get total count for pagination
        const total = await Order.countDocuments(query);

        res.status(200).json({
            status: 'success',
            results: orders.length,
            total,
            totalPages: Math.ceil(total / pageSize),
            currentPage: parseInt(page),
            data: {
                orders
            }
        });
    } catch (error) {
        console.error('Get all orders error:', error);
        res.status(400).json({
            status: 'fail',
            message: error.message
        });
    }
};

// Get a single order
exports.getOrder = async (req, res) => {
    try {
        const id = req.params.id;
        const userId = req.user.id;

        const order = await Order.findOne({ _id: id, userId });

        if (!order) {
            return res.status(404).json({
                status: 'fail',
                message: 'Order not found'
            });
        }

        res.status(200).json({
            status: 'success',
            data: {
                order
            }
        });
    } catch (error) {
        console.error('Get order error:', error);
        res.status(400).json({
            status: 'fail',
            message: error.message
        });
    }
};

// Update order status (admin only)
exports.updateOrderStatus = async (req, res) => {
    try {
        const id = req.params.id;
        const { status } = req.body;

        if (!status) {
            return res.status(400).json({
                status: 'fail',
                message: 'Status is required'
            });
        }

        const order = await Order.findById(id);

        if (!order) {
            return res.status(404).json({
                status: 'fail',
                message: 'Order not found'
            });
        }

        order.status = status;
        await order.save();

        res.status(200).json({
            status: 'success',
            data: {
                order
            }
        });
    } catch (error) {
        console.error('Update order status error:', error);
        res.status(400).json({
            status: 'fail',
            message: error.message
        });
    }
};

// Cancel order (customer)
exports.cancelOrder = async (req, res) => {
    try {
        const id = req.params.id;
        const userId = req.user.id;

        const order = await Order.findOne({ _id: id, userId });

        if (!order) {
            return res.status(404).json({
                status: 'fail',
                message: 'Order not found'
            });
        }

        // Only allow cancellation if order is pending or processing
        if (order.status !== 'pending' && order.status !== 'processing') {
            return res.status(400).json({
                status: 'fail',
                message: `Cannot cancel order with status: ${order.status}`
            });
        }

        order.status = 'cancelled';
        await order.save();

        res.status(200).json({
            status: 'success',
            data: {
                order
            }
        });
    } catch (error) {
        console.error('Cancel order error:', error);
        res.status(400).json({
            status: 'fail',
            message: error.message
        });
    }
};
