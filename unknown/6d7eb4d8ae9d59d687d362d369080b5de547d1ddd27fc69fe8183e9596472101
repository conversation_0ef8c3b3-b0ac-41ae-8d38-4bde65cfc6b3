const express = require("express");
const cors = require("cors");
const dotenv = require("dotenv");
const path = require("path");
const { createProxyMiddleware } = require("http-proxy-middleware");
const routes = require("./routes");

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '.env') });

// Log environment variables for debugging
console.log('Environment variables:');
console.log('PORT:', process.env.PORT);
console.log('SERVICE_NAME:', process.env.SERVICE_NAME);
console.log('AUTH_SERVICE_URL:', process.env.AUTH_SERVICE_URL);
console.log('PRODUCT_SERVICE_URL:', process.env.PRODUCT_SERVICE_URL);
console.log('ORDER_SERVICE_URL:', process.env.ORDER_SERVICE_URL);
console.log('CLIENT_URL:', process.env.CLIENT_URL);

// Initialize express app
const app = express();

// Middleware
app.use(
  cors({
    origin: [process.env.CLIENT_URL, 'http://localhost:3000', 'http://localhost:3001'],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);
app.use(express.json({ limit: '10mb' }));

// Routes
app.use(routes);

// Health check endpoint
app.get("/health", (req, res) => {
  res.status(200).json({
    status: "success",
    service: process.env.SERVICE_NAME,
    message: "API Gateway is running"
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error("Global error handler:", err);
  res.status(500).json({
    status: "error",
    message: "Internal server error",
    error: process.env.NODE_ENV === "development" ? err.message : undefined
  });
});

// Start server
const PORT = process.env.PORT || 5002;
app.listen(PORT, () => {
  console.log(`API Gateway running on http://localhost:${PORT}`);
});
