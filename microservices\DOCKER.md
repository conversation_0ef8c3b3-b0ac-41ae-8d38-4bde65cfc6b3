# Docker Setup for Chinioti Wooden Art Microservices

This document provides instructions for running the Chinioti Wooden Art microservices using Docker.

## Prerequisites

- Docker installed on your machine
- Docker Compose installed on your machine (either as a standalone tool or as part of Docker Desktop)

### Installing Docker and Docker Compose

#### macOS

1. Download and install Docker Desktop for Mac from the official website:
   https://www.docker.com/products/docker-desktop/

2. After installation, open Docker Desktop to ensure it's running.

#### Alternative: Using Homebrew

If you prefer using Homebrew:

```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Add Homebrew to your PATH
echo 'eval "$(/opt/homebrew/bin/brew shellenv)"' >> ~/.zprofile
eval "$(/opt/homebrew/bin/brew shellenv)"

# Install Docker Desktop
brew install --cask docker
```

#### Windows

1. Download and install Docker Desktop for Windows from the official website:
   https://www.docker.com/products/docker-desktop/

2. After installation, open Docker Desktop to ensure it's running.

#### Linux

Follow the official Docker installation guide for your specific Linux distribution:
https://docs.docker.com/engine/install/

## Services Architecture

The application is containerized into the following services:

1. **API Gateway** (Port 5002) - Entry point for all client requests
2. **Auth Service** (Port 4001) - Handles user authentication and management
3. **Product Service** (Port 4002) - Manages product data and operations
4. **Order Service** (Port 4003) - Handles order processing and management

## Getting Started

### Building the Docker Images

To build all the Docker images, run:

```bash
./docker-build.sh
```

This will build Docker images for all microservices.

### Running the Containers

To start all the containers, run:

```bash
./docker-run.sh
```

This will start all services in detached mode.

### Stopping the Containers

To stop all the containers, run:

```bash
./docker-stop.sh
```

## Accessing the Services

Once the containers are running, you can access the services at:

- API Gateway: http://localhost:5002
- Auth Service: http://localhost:4001
- Product Service: http://localhost:4002
- Order Service: http://localhost:4003

## Viewing Logs

### Using Docker Compose V1

If you're using the traditional Docker Compose:

```bash
# View logs for all services
docker-compose logs -f

# View logs for a specific service
docker-compose logs -f <service-name>
```

### Using Docker Compose V2

If you're using Docker Compose V2 (integrated with Docker CLI):

```bash
# View logs for all services
docker compose logs -f

# View logs for a specific service
docker compose logs -f <service-name>
```

Replace `<service-name>` with one of: `api-gateway`, `auth-service`, `product-service`, or `order-service`.

## Container Management

### Using Docker Compose V1

#### Restarting a Service

To restart a specific service, run:

```bash
docker-compose restart <service-name>
```

#### Rebuilding a Service

If you make changes to a service, you need to rebuild its Docker image:

```bash
docker-compose build <service-name>
docker-compose up -d <service-name>
```

### Using Docker Compose V2

#### Restarting a Service

To restart a specific service, run:

```bash
docker compose restart <service-name>
```

#### Rebuilding a Service

If you make changes to a service, you need to rebuild its Docker image:

```bash
docker compose build <service-name>
docker compose up -d <service-name>
```

## Troubleshooting

### Docker Installation Issues

If you're having issues with Docker installation:

1. For macOS, ensure Docker Desktop is properly installed and running:
   - Check if the Docker Desktop icon appears in the menu bar
   - Open Docker Desktop and check its status

2. If you installed via Homebrew and are having issues:
   ```bash
   # Repair Homebrew
   brew doctor

   # Reinstall Docker
   brew reinstall --cask docker
   ```

### Network Issues

If services can't communicate with each other, check the Docker network:

```bash
# List all Docker networks
docker network ls

# Inspect the microservices network
docker network inspect microservices_microservices-network
```

### Container Status

To check the status of all containers:

```bash
# Using Docker Compose V1
docker-compose ps

# Using Docker Compose V2
docker compose ps
```

### Accessing Container Shell

To access a shell in a running container:

```bash
# Using Docker Compose V1
docker-compose exec <service-name> sh

# Using Docker Compose V2
docker compose exec <service-name> sh
```

### Checking Logs for Errors

If a service is not working properly, check its logs:

```bash
# Using Docker Compose V1
docker-compose logs <service-name>

# Using Docker Compose V2
docker compose logs <service-name>
```

### Restarting Docker

If you're experiencing persistent issues, try restarting Docker:

1. On macOS:
   - Click the Docker icon in the menu bar
   - Select "Restart"
   - Wait for Docker to restart completely

2. On Windows:
   - Right-click the Docker icon in the system tray
   - Select "Restart"
   - Wait for Docker to restart completely

## Environment Variables

Environment variables are defined in the `docker-compose.yml` file. If you need to change any configuration, update the environment section for the respective service in this file.
