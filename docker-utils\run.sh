#!/bin/bash

# Script to start Docker containers for the server application

echo "Starting Docker containers for Chinioti Wooden Art server..."

# Navigate to the server directory (in case script is run from elsewhere)
cd "$(dirname "$0")"

# Check if .env file exists
if [ ! -f .env ]; then
  echo "Error: .env file not found!"
  echo "Please create a .env file with the required environment variables."
  exit 1
fi

# Check if docker-compose is available
if command -v docker-compose &> /dev/null; then
  echo "Using docker-compose..."
  docker-compose up -d
elif command -v docker &> /dev/null; then
  echo "Using docker compose v2..."
  docker compose up -d
else
  echo "Error: Neither docker-compose nor docker compose is available."
  echo "Please install Docker and Dock<PERSON> Compose before running this script."
  exit 1
fi

echo "Containers started successfully!"
echo "Server is running at http://localhost:5002"
echo "Redis is running on port 6379"
