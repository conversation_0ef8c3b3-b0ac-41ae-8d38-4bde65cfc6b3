{"name": "auth-service", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"auth-service": "file:", "bcrypt": "^5.1.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "crypto-random-string": "^5.0.0", "dotenv": "^16.5.0", "express": "^5.1.0", "google-auth-library": "^9.15.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "nodemailer": "^7.0.2"}, "devDependencies": {"nodemon": "^3.1.9"}}