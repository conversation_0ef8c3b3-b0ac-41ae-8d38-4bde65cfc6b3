const Contact = require('../models/Contact');
const { sendContactFormEmail, sendContactAutoReplyEmail } = require('../utils/emailService');

/**
 * Submit a contact form
 * @route POST /api/contact
 * @access Public
 */
exports.submitContactForm = async (req, res) => {
    try {
        const { name, email, phone, subject, message } = req.body;

        // Validate required fields
        if (!name || !email || !subject || !message) {
            return res.status(400).json({
                status: 'fail',
                message: 'Please provide all required fields: name, email, subject, and message'
            });
        }

        // Validate email format
        const emailRegex = /\S+@\S+\.\S+/;
        if (!emailRegex.test(email)) {
            return res.status(400).json({
                status: 'fail',
                message: 'Please provide a valid email address'
            });
        }

        // Create new contact entry in database
        const newContact = await Contact.create({
            name,
            email,
            phone: phone || '',
            subject,
            message
        });

        // Send email to admin
        await sendContactFormEmail({
            name,
            email,
            phone,
            subject,
            message
        });

        // Send auto-reply email to user
        await sendContactAutoReplyEmail(email, name);

        // Return success response
        res.status(201).json({
            status: 'success',
            message: 'Your message has been sent successfully. We will get back to you soon.',
            data: {
                contact: {
                    id: newContact._id,
                    name: newContact.name,
                    email: newContact.email,
                    subject: newContact.subject,
                    createdAt: newContact.createdAt
                }
            }
        });
    } catch (error) {
        console.error('Error submitting contact form:', error);
        res.status(500).json({
            status: 'error',
            message: 'An error occurred while submitting your message. Please try again later.'
        });
    }
};

/**
 * Get all contact form submissions
 * @route GET /api/contact
 * @access Private (Admin only)
 */
exports.getAllContacts = async (req, res) => {
    try {
        const contacts = await Contact.find().sort({ createdAt: -1 });
        
        res.status(200).json({
            status: 'success',
            results: contacts.length,
            data: {
                contacts
            }
        });
    } catch (error) {
        console.error('Error fetching contacts:', error);
        res.status(500).json({
            status: 'error',
            message: 'An error occurred while fetching contacts'
        });
    }
};

/**
 * Get a single contact form submission
 * @route GET /api/contact/:id
 * @access Private (Admin only)
 */
exports.getContact = async (req, res) => {
    try {
        const contact = await Contact.findById(req.params.id);
        
        if (!contact) {
            return res.status(404).json({
                status: 'fail',
                message: 'Contact not found'
            });
        }

        // Update status to 'read' if it was 'new'
        if (contact.status === 'new') {
            contact.status = 'read';
            await contact.save();
        }
        
        res.status(200).json({
            status: 'success',
            data: {
                contact
            }
        });
    } catch (error) {
        console.error('Error fetching contact:', error);
        res.status(500).json({
            status: 'error',
            message: 'An error occurred while fetching the contact'
        });
    }
};

/**
 * Update contact status
 * @route PATCH /api/contact/:id
 * @access Private (Admin only)
 */
exports.updateContactStatus = async (req, res) => {
    try {
        const { status } = req.body;
        
        if (!status || !['new', 'read', 'replied'].includes(status)) {
            return res.status(400).json({
                status: 'fail',
                message: 'Please provide a valid status: new, read, or replied'
            });
        }
        
        const contact = await Contact.findByIdAndUpdate(
            req.params.id,
            { status },
            { new: true, runValidators: true }
        );
        
        if (!contact) {
            return res.status(404).json({
                status: 'fail',
                message: 'Contact not found'
            });
        }
        
        res.status(200).json({
            status: 'success',
            data: {
                contact
            }
        });
    } catch (error) {
        console.error('Error updating contact status:', error);
        res.status(500).json({
            status: 'error',
            message: 'An error occurred while updating the contact status'
        });
    }
};
