const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    maxlength: [25, 'Name cannot exceed 50 characters'],
    match: [/^[A-Za-z\s]+$/, 'Name can only contain alphabets and spaces'],
  },
  email: {
    type: String,
    required: true,
    lowercase: true,
    match: [/\S+@\S+\.\S+/, 'Please enter a valid email'],
    unique: true,
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 6 characters long'],
    // Relaxed password validation to make testing easier
    match: [
      /^(?=.*[a-zA-Z])(?=.*\d).+$/,
      'Password must contain at least one letter and one number',
    ],
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    match: [/^\d{11}$/, 'Phone number must be exactly 11 digits'],
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
  resetPasswordToken: {
    type: String,
    default: null,
  },
  resetPasswordOTP: {
    type: String,
    default: null,
  },
  resetPasswordExpires: {
    type: Date,
    default: null,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model('User', userSchema);
