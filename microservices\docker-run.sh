#!/bin/bash

# Script to run Docker containers for all microservices

echo "Starting Docker containers for all microservices..."

# Navigate to microservices directory
cd "$(dirname "$0")"

# Check if docker-compose is available
if command -v docker-compose &> /dev/null; then
    echo "Using docker-compose..."
    docker-compose up -d
elif command -v docker &> /dev/null; then
    echo "Using docker compose v2..."
    docker compose up -d
else
    echo "Error: Neither docker-compose nor docker compose is available."
    echo "Please install Docker and Docker Compose before running this script."
    exit 1
fi

echo "Docker containers started successfully!"
echo "API Gateway: http://localhost:5002"
echo "Auth Service: http://localhost:4001"
echo "Product Service: http://localhost:4002"
echo "Order Service: http://localhost:4003"
echo ""
echo "To view logs, run: docker-compose logs -f"
echo "To stop the services, run: ./docker-stop.sh"
