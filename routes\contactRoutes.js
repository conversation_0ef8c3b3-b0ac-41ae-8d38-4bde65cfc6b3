const express = require('express');
const router = express.Router();
const contactController = require('../controllers/contactController');
const { protect } = require('../middleware/authMiddleware');

// Public route - anyone can submit a contact form
router.post('/', contactController.submitContactForm);

// Protected routes - admin only
router.get('/', protect, contactController.getAllContacts);
router.get('/:id', protect, contactController.getContact);
router.patch('/:id', protect, contactController.updateContactStatus);

module.exports = router;
