const multer = require('multer');
const { GridFsStorage } = require('multer-gridfs-storage');
const path = require('path');
const crypto = require('crypto');
const { config } = require('dotenv');
config();

// Create GridFS storage engine
const storage = new GridFsStorage({
  url: process.env.MONGO_URI,
  options: { useNewUrlParser: true, useUnifiedTopology: true },
  file: (req, file) => {
    return new Promise((resolve, reject) => {
      // Generate a random 16 character hex string
      crypto.randomBytes(16, (err, buf) => {
        if (err) {
          return reject(err);
        }
        
        const fileInfo = {
          filename: file.fieldname + '-' + Date.now() + '-' + buf.toString('hex') + path.extname(file.originalname),
          bucketName: 'uploads',
          metadata: {
            originalname: file.originalname,
            mimetype: file.mimetype,
            uploadDate: new Date()
          }
        };
        
        resolve(fileInfo);
      });
    });
  }
});

// File filter to accept only images
const fileFilter = (req, file, cb) => {
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('Not an image! Please upload only images.'), false);
  }
};

// Create multer upload instance
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB limit
  }
});

// Middleware for handling product uploads (main image and additional images)
const uploadProductImages = upload.fields([
  { name: 'image', maxCount: 1 },
  { name: 'additionalImages', maxCount: 5 }
]);

module.exports = {
  uploadProductImages
};
