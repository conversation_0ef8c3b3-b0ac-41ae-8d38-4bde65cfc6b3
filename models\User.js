const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    maxlength: [25, 'Name cannot exceed 50 characters'],
    match: [/^[A-Za-z\s]+$/, 'Name can only contain alphabets and spaces'],
  },
  email: {
    type: String,
    required: true,
    lowercase: true,
    match: [/\S+@\S+\.\S+/, 'Please enter a valid email'],
    unique: true,
  },
  password: {
    type: String,
    required: [true, 'Password is required'],
    minlength: [8, 'Password must be at least 6 characters long'],
    match: [
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/,
      'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    ],
  },
  phone: {
    type: String,
    required: [true, 'Phone number is required'],
    match: [/^\d{11}$/, 'Phone number must be exactly 11 digits'],
  },
  resetPasswordToken: {
    type: String,
    default: null,
  },
  resetPasswordOTP: {
    type: String,
    default: null,
  },
  resetPasswordExpires: {
    type: Date,
    default: null,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model('User', userSchema);
