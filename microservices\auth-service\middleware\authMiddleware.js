const jwt = require('jsonwebtoken');
const User = require('../models/User');

// Middleware to validate registration data
exports.validateRegistration = (req, res, next) => {
  const { name, email, password, phone } = req.body;

  // Check if all required fields are present
  if (!name || !email || !password || !phone) {
    return res.status(400).json({
      success: false,
      message: 'Please provide all required fields',
    });
  }

  // Validate name (only alphabets and spaces)
  if (!/^[A-Za-z\s]+$/.test(name)) {
    return res.status(400).json({
      success: false,
      message: 'Name can only contain alphabets and spaces',
    });
  }

  // Validate email
  if (!/\S+@\S+\.\S+/.test(email)) {
    return res.status(400).json({
      success: false,
      message: 'Please provide a valid email',
    });
  }

  // Validate password (min 8 chars, at least one letter and one number)
  if (!/^(?=.*[a-zA-Z])(?=.*\d).{8,}$/.test(password)) {
    return res.status(400).json({
      success: false,
      message:
        'Password must be at least 8 characters long and contain at least one letter and one number',
    });
  }

  // Validate phone (exactly 11 digits)
  if (!/^\d{11}$/.test(phone)) {
    return res.status(400).json({
      success: false,
      message: 'Phone number must be exactly 11 digits',
    });
  }

  next();
};

// Middleware to validate login data
exports.validateLogin = (req, res, next) => {
  const { email, password } = req.body;

  // Check if email and password are provided
  if (!email || !password) {
    return res.status(400).json({
      success: false,
      message: 'Please provide email and password',
    });
  }

  next();
};

// Middleware to protect routes
exports.protect = async (req, res, next) => {
  try {
    // 1) Get token from Authorization header
    let token;
    if (
      req.headers.authorization &&
      req.headers.authorization.startsWith('Bearer')
    ) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'You are not logged in. Please log in to get access.',
      });
    }

    // 2) Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // 3) Check if user still exists
    const currentUser = await User.findById(decoded.userId);
    if (!currentUser) {
      return res.status(401).json({
        success: false,
        message: 'The user belonging to this token no longer exists.',
      });
    }

    // 4) Grant access to protected route
    req.user = currentUser;
    next();
  } catch (error) {
    console.error('Auth middleware error:', error);
    return res.status(401).json({
      success: false,
      message: 'Invalid token or token expired',
    });
  }
};
