const VideoBlog = require('../models/VideoBlog');
const { clearCache } = require('../config/redis');
const { 
  deleteFileById, 
  findFileById, 
  createReadStream,
  getGridFSBucket 
} = require('../config/gridfs');
const mongoose = require('mongoose');

/**
 * Create a new video blog
 * @route POST /api/video-blogs
 * @access Private (Admin only)
 */
const createVideoBlog = async (req, res) => {
  try {
    const { title } = req.body;
    const files = req.files;

    // Validate required fields
    if (!title) {
      return res.status(400).json({
        status: 'error',
        message: 'Title is required'
      });
    }

    if (!files || !files.video || files.video.length === 0) {
      return res.status(400).json({
        status: 'error',
        message: 'Video file is required'
      });
    }

    const videoFile = files.video[0];
    const thumbnailFile = files.thumbnail ? files.thumbnail[0] : null;

    // Create video blog document
    const videoBlogData = {
      title: title.trim(),
      videoFileId: videoFile.id,
      videoFilename: videoFile.filename,
      mimetype: videoFile.mimetype,
      fileSize: videoFile.size,
      uploadedBy: req.user.id, // Assuming user is attached to req by auth middleware
    };

    // Add thumbnail data if provided
    if (thumbnailFile) {
      videoBlogData.thumbnailFileId = thumbnailFile.id;
      videoBlogData.thumbnailFilename = thumbnailFile.filename;
    }

    const videoBlog = new VideoBlog(videoBlogData);
    await videoBlog.save();

    // Clear cache
    await clearCache('video-blogs:*');

    res.status(201).json({
      status: 'success',
      message: 'Video blog created successfully',
      data: {
        videoBlog
      }
    });

  } catch (error) {
    console.error('Error creating video blog:', error);
    
    // Clean up uploaded files if video blog creation fails
    if (req.files) {
      if (req.files.video && req.files.video[0]) {
        await deleteFileById(req.files.video[0].id);
      }
      if (req.files.thumbnail && req.files.thumbnail[0]) {
        await deleteFileById(req.files.thumbnail[0].id);
      }
    }

    res.status(500).json({
      status: 'error',
      message: 'Failed to create video blog',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get all video blogs with pagination
 * @route GET /api/video-blogs
 * @access Public
 */
const getAllVideoBlogs = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    // Build query
    const query = { isActive: true };

    // Add search functionality
    if (req.query.search) {
      query.$text = { $search: req.query.search };
    }

    // Get total count for pagination
    const total = await VideoBlog.countDocuments(query);

    // Fetch video blogs
    const videoBlogs = await VideoBlog.find(query)
      .populate('uploadedBy', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean();

    // Calculate pagination info
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    res.status(200).json({
      status: 'success',
      data: {
        videoBlogs,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: total,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage
        }
      }
    });

  } catch (error) {
    console.error('Error fetching video blogs:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch video blogs',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Get a single video blog by ID
 * @route GET /api/video-blogs/:id
 * @access Public
 */
const getVideoBlogById = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid video blog ID'
      });
    }

    const videoBlog = await VideoBlog.findOne({ 
      _id: id, 
      isActive: true 
    }).populate('uploadedBy', 'name email');

    if (!videoBlog) {
      return res.status(404).json({
        status: 'error',
        message: 'Video blog not found'
      });
    }

    // Increment view count
    videoBlog.views += 1;
    await videoBlog.save();

    res.status(200).json({
      status: 'success',
      data: {
        videoBlog
      }
    });

  } catch (error) {
    console.error('Error fetching video blog:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch video blog',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Stream video file
 * @route GET /api/video-blogs/stream/:fileId
 * @access Public
 */
const streamVideo = async (req, res) => {
  try {
    const { fileId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(fileId)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid file ID'
      });
    }

    // Find the file in GridFS
    const file = await findFileById(fileId);
    
    if (!file) {
      return res.status(404).json({
        status: 'error',
        message: 'Video file not found'
      });
    }

    // Set appropriate headers for video streaming
    res.set({
      'Content-Type': file.metadata.mimetype || 'video/mp4',
      'Content-Length': file.length,
      'Accept-Ranges': 'bytes',
      'Cache-Control': 'public, max-age=31536000' // Cache for 1 year
    });

    // Create and pipe the read stream
    const readStream = createReadStream(fileId);
    readStream.pipe(res);

    readStream.on('error', (error) => {
      console.error('Error streaming video:', error);
      if (!res.headersSent) {
        res.status(500).json({
          status: 'error',
          message: 'Error streaming video'
        });
      }
    });

  } catch (error) {
    console.error('Error streaming video:', error);
    if (!res.headersSent) {
      res.status(500).json({
        status: 'error',
        message: 'Failed to stream video',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
};

/**
 * Stream thumbnail image
 * @route GET /api/video-blogs/thumbnail/:fileId
 * @access Public
 */
const streamThumbnail = async (req, res) => {
  try {
    const { fileId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(fileId)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid file ID'
      });
    }

    // Find the file in GridFS
    const file = await findFileById(fileId);

    if (!file) {
      return res.status(404).json({
        status: 'error',
        message: 'Thumbnail file not found'
      });
    }

    // Set appropriate headers for image
    res.set({
      'Content-Type': file.metadata.mimetype || 'image/jpeg',
      'Content-Length': file.length,
      'Cache-Control': 'public, max-age=31536000' // Cache for 1 year
    });

    // Create and pipe the read stream
    const readStream = createReadStream(fileId);
    readStream.pipe(res);

    readStream.on('error', (error) => {
      console.error('Error streaming thumbnail:', error);
      if (!res.headersSent) {
        res.status(500).json({
          status: 'error',
          message: 'Error streaming thumbnail'
        });
      }
    });

  } catch (error) {
    console.error('Error streaming thumbnail:', error);
    if (!res.headersSent) {
      res.status(500).json({
        status: 'error',
        message: 'Failed to stream thumbnail',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      });
    }
  }
};

/**
 * Update video blog
 * @route PATCH /api/video-blogs/:id
 * @access Private (Admin only)
 */
const updateVideoBlog = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, isActive } = req.body;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid video blog ID'
      });
    }

    const videoBlog = await VideoBlog.findById(id);

    if (!videoBlog) {
      return res.status(404).json({
        status: 'error',
        message: 'Video blog not found'
      });
    }

    // Update fields
    if (title !== undefined) {
      videoBlog.title = title.trim();
    }
    if (isActive !== undefined) {
      videoBlog.isActive = isActive;
    }

    await videoBlog.save();

    // Clear cache
    await clearCache('video-blogs:*');

    res.status(200).json({
      status: 'success',
      message: 'Video blog updated successfully',
      data: {
        videoBlog
      }
    });

  } catch (error) {
    console.error('Error updating video blog:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update video blog',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

/**
 * Delete video blog
 * @route DELETE /api/video-blogs/:id
 * @access Private (Admin only)
 */
const deleteVideoBlog = async (req, res) => {
  try {
    const { id } = req.params;

    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid video blog ID'
      });
    }

    const videoBlog = await VideoBlog.findById(id);

    if (!videoBlog) {
      return res.status(404).json({
        status: 'error',
        message: 'Video blog not found'
      });
    }

    // Delete video file from GridFS
    await deleteFileById(videoBlog.videoFileId);

    // Delete thumbnail file if exists
    if (videoBlog.thumbnailFileId) {
      await deleteFileById(videoBlog.thumbnailFileId);
    }

    // Delete video blog document
    await VideoBlog.findByIdAndDelete(id);

    // Clear cache
    await clearCache('video-blogs:*');

    res.status(200).json({
      status: 'success',
      message: 'Video blog deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting video blog:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to delete video blog',
      error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
};

module.exports = {
  createVideoBlog,
  getAllVideoBlogs,
  getVideoBlogById,
  streamVideo,
  streamThumbnail,
  updateVideoBlog,
  deleteVideoBlog
};
