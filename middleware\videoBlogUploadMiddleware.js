const multer = require('multer');
const { GridFsStorage } = require('multer-gridfs-storage');
const path = require('path');
const crypto = require('crypto');
const { config } = require('dotenv');
config();

// Create GridFS storage engine for video uploads
const videoStorage = new GridFsStorage({
  url: process.env.MONGO_URI,
  options: { useNewUrlParser: true, useUnifiedTopology: true },
  file: (req, file) => {
    return new Promise((resolve, reject) => {
      // Generate a random 16 character hex string
      crypto.randomBytes(16, (err, buf) => {
        if (err) {
          return reject(err);
        }
        
        const fileInfo = {
          filename: file.fieldname + '-' + Date.now() + '-' + buf.toString('hex') + path.extname(file.originalname),
          bucketName: 'uploads',
          metadata: {
            originalname: file.originalname,
            mimetype: file.mimetype,
            uploadDate: new Date(),
            fileType: file.fieldname // 'video' or 'thumbnail'
          }
        };
        
        resolve(fileInfo);
      });
    });
  }
});

// File filter to accept only video files and images (for thumbnails)
const videoFileFilter = (req, file, cb) => {
  if (file.fieldname === 'video') {
    // Accept video files
    if (file.mimetype.startsWith('video/')) {
      // Check for supported video formats
      const supportedFormats = [
        'video/mp4',
        'video/mpeg',
        'video/quicktime',
        'video/x-msvideo', // .avi
        'video/webm',
        'video/ogg'
      ];
      
      if (supportedFormats.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error('Unsupported video format! Please upload MP4, AVI, MOV, WebM, or OGG files.'), false);
      }
    } else {
      cb(new Error('Not a video file! Please upload only video files.'), false);
    }
  } else if (file.fieldname === 'thumbnail') {
    // Accept image files for thumbnails
    if (file.mimetype.startsWith('image/')) {
      const supportedImageFormats = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp'
      ];
      
      if (supportedImageFormats.includes(file.mimetype)) {
        cb(null, true);
      } else {
        cb(new Error('Unsupported image format for thumbnail! Please upload JPEG, PNG, or WebP files.'), false);
      }
    } else {
      cb(new Error('Thumbnail must be an image file!'), false);
    }
  } else {
    cb(new Error('Invalid field name!'), false);
  }
};

// Create multer upload instance for video blogs
const uploadVideoBlog = multer({
  storage: videoStorage,
  fileFilter: videoFileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit for videos
    files: 2 // Maximum 2 files (video + thumbnail)
  }
});

// Middleware for handling video blog uploads (video file and optional thumbnail)
const uploadVideoBlogFiles = uploadVideoBlog.fields([
  { name: 'video', maxCount: 1 },
  { name: 'thumbnail', maxCount: 1 }
]);

// Error handling middleware for multer errors
const handleUploadErrors = (error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        status: 'error',
        message: 'File too large. Maximum size allowed is 100MB for videos and 5MB for thumbnails.'
      });
    } else if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({
        status: 'error',
        message: 'Too many files. Maximum 1 video and 1 thumbnail allowed.'
      });
    } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({
        status: 'error',
        message: 'Unexpected field name. Use "video" for video file and "thumbnail" for thumbnail image.'
      });
    }
  } else if (error) {
    return res.status(400).json({
      status: 'error',
      message: error.message
    });
  }
  next();
};

module.exports = {
  uploadVideoBlogFiles,
  handleUploadErrors
};
