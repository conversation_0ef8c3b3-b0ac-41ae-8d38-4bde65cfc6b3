#!/bin/bash

# <PERSON><PERSON>t to stop Docker containers for all microservices

echo "Stopping Docker containers for all microservices..."

# Navigate to microservices directory
cd "$(dirname "$0")"

# Check if docker-compose is available
if command -v docker-compose &> /dev/null; then
    echo "Using docker-compose..."
    docker-compose down
elif command -v docker &> /dev/null; then
    echo "Using docker compose v2..."
    docker compose down
else
    echo "Error: Neither docker-compose nor docker compose is available."
    echo "Please install Docker and Docker Compose before running this script."
    exit 1
fi

echo "Docker containers stopped successfully!"
