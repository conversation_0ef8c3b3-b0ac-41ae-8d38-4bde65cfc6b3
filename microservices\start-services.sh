#!/bin/bash

# Start all microservices

# Function to start a service
start_service() {
  local service_name=$1
  local service_dir=$2
  local start_command=${3:-"npm run dev"}

  echo "Starting $service_name..."
  cd $service_dir
  npm install
  $start_command &
  cd ..
  sleep 2
}

# Navigate to microservices directory
cd "$(dirname "$0")"

# Start services in order
start_service "Auth Service" "auth-service" "npm run dev"
start_service "Product Service" "product-service" "npm run dev"
start_service "Order Service" "order-service" "npm run dev"
start_service "API Gateway" "api-gateway" "node server.js"

echo "All services started!"
echo "API Gateway: http://localhost:5002"
echo "Auth Service: http://localhost:4001"
echo "Product Service: http://localhost:4002"
echo "Order Service: http://localhost:4003"

# Keep script running
wait
