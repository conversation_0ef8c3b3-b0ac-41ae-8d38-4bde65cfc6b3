const jwt = require('jsonwebtoken');
const axios = require('axios');

// Middleware to protect routes
exports.protect = async (req, res, next) => {
    try {
        // 1) Get token from Authorization header
        let token;
        if (
            req.headers.authorization &&
            req.headers.authorization.startsWith('Bearer')
        ) {
            token = req.headers.authorization.split(' ')[1];
        }

        if (!token) {
            return res.status(401).json({
                status: 'fail',
                message: 'You are not logged in. Please log in to get access.',
            });
        }

        try {
            // Verify token locally
            const decoded = jwt.verify(token, process.env.JWT_SECRET);

            // Validate with auth service - using Docker container name in containerized environment
            const authServiceUrl = process.env.AUTH_SERVICE_URL || 'http://localhost:4001';
            console.log(`Validating token with auth service at: ${authServiceUrl}`);

            const response = await axios.get(`${authServiceUrl}/api/users/${decoded.userId}`, {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });

            if (response.data.status !== 'success') {
                throw new Error('Failed to validate user with auth service');
            }

            // Add user info to request
            req.user = {
                id: decoded.userId
            };

            next();
        } catch (error) {
            console.error('Token validation error:', error.message);
            return res.status(401).json({
                status: 'fail',
                message: 'Invalid token or token expired'
            });
        }
    } catch (error) {
        return res.status(401).json({
            status: 'fail',
            message: 'Authentication error'
        });
    }
};
