version: "3.8"

services:
  # Server Application
  server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: chinioti-server
    ports:
      - "5002:5002"
    environment:
      - PORT=5002
      - MONGO_URI=${MONGO_URI}
      - JWT_SECRET=${JWT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - EMAIL_USER=${EMAIL_USER}
      - EMAIL_PASSWORD=${EMAIL_PASSWORD}
      - GOOGLE_REDIRECT_URI=${GOOGLE_REDIRECT_URI}
      - REDIS_URL=${REDIS_URL}
      - DEPLOYED_URL=${DEPLOYED_URL}
    volumes:
      - ./.env:/app/.env
    networks:
      - chinioti-network
    restart: unless-stopped

# Using Upstash Redis cloud service instead of local Redis container

networks:
  chinioti-network:
    driver: bridge
