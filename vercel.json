{"version": 2, "functions": {"api/index.js": {"maxDuration": 30, "memory": 1024}, "api/health.js": {"maxDuration": 10, "memory": 512}}, "routes": [{"src": "/api/health", "dest": "api/health.js"}, {"src": "/health", "dest": "api/health.js"}, {"src": "/(.*)", "dest": "api/index.js"}], "headers": [{"source": "/api/(.*)", "headers": [{"key": "Cache-Control", "value": "s-maxage=60, stale-while-revalidate"}]}, {"source": "/api/health", "headers": [{"key": "Cache-Control", "value": "s-maxage=30, stale-while-revalidate"}]}], "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}}